# 🌟 Cleanify Auto Upload Bot v2.0

Bot otomatis canggih untuk Cleanify dengan sistem referral terintegrasi, keamanan tingkat enterprise, dan fitur batch processing.

## ✨ Fitur Utama

### 🚀 Core Features
- **Auto Upload & Claim**: Upload gambar otomatis ke IPFS dan claim reward
- **Smart Contract Integration**: Interaksi langsung dengan VeChain blockchain
- **Batch Processing**: Proses multiple gambar sekaligus
- **Wallet Management**: Sistem manajemen wallet yang aman dengan enkripsi

### 🎯 Sistem Referral
- **Auto Referral**: Otomatis apply referral code `LD4BK` untuk akun baru
- **Referral Tracking**: Tracking reward referral secara real-time
- **Referral Management**: Tools khusus untuk mengelola referral
- **Reward Claiming**: Auto claim referral rewards

### 🔒 Keamanan Enterprise
- **Private Key Encryption**: Enkripsi AES-256 untuk private keys
- **Secure Storage**: Penyimpanan data terenkripsi
- **Random User Agents**: Anti-detection dengan randomized headers
- **Proxy Support**: Dukungan proxy untuk anonymity

### 📊 Advanced Features
- **Comprehensive Logging**: System logging dengan multiple levels
- **Error Handling**: Robust error handling dengan retry mechanism
- **Image Optimization**: Auto optimize gambar sebelum upload
- **Statistics Tracking**: Detailed statistics dan reporting
- **Configuration Management**: Flexible configuration system

## 🛠️ Installation

### 1. Clone Repository
```bash
git clone <repository-url>
cd cleanify-auto-create-upload-master
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Setup Bot
```bash
npm run setup
```

### 4. Configure Environment
Edit file `.env` dan sesuaikan dengan kebutuhan:
```env
REFERRAL_CODE=LD4BK
AUTO_APPLY_REFERRAL=true
ENCRYPTION_KEY=your-generated-key
```

## 🎯 Referral System

### Konfigurasi Referral
Bot sudah dikonfigurasi dengan referral code `LD4BK` dari link:
`https://app.cleanify.vet/?referralCode=LD4BK`

### Referral Commands
```bash
# Lihat info referral
npm run referral:info

# Lihat statistik referral
npm run referral:stats

# Cek referral rewards
npm run referral:check

# Claim referral rewards
npm run referral:claim

# Apply referral ke wallet existing
npm run referral:apply
```

## 🚀 Usage

### Basic Usage
```bash
# Jalankan bot (single/batch mode)
npm start

# Jalankan dengan legacy script
npm run legacy
```

### Advanced Usage
```bash
# Setup initial configuration
npm run setup

# Run tests
npm test

# Manage referrals
npm run referral
```

## 📁 File Structure

```
cleanify-auto-create-upload-master/
├── src/
│   ├── utils/
│   │   ├── logger.js          # Advanced logging system
│   │   ├── crypto.js          # Encryption utilities
│   │   ├── network.js         # Network management
│   │   └── fileManager.js     # File operations
│   ├── wallet/
│   │   └── walletManager.js   # Wallet management
│   └── referral/
│       └── referralManager.js # Referral system
├── data/
│   ├── accounts.json          # Encrypted wallet data
│   ├── referral_data.json     # Referral statistics
│   └── bot_stats.json         # Bot statistics
├── images/                    # Directory untuk gambar
├── logs/                      # Log files
├── config.json               # Main configuration
├── .env                      # Environment variables
├── upload_new.js            # Main bot script v2.0
├── referral.js              # Referral management
├── setup.js                 # Setup script
└── upload.js                # Legacy script
```

## ⚙️ Configuration

### config.json
```json
{
  "referral": {
    "enabled": true,
    "referralCode": "LD4BK",
    "autoApplyReferral": true,
    "trackReferralRewards": true,
    "referralRewardThreshold": 10
  },
  "security": {
    "encryptPrivateKeys": true,
    "randomizeUserAgent": true,
    "useProxy": false
  },
  "automation": {
    "batchMode": false,
    "batchSize": 5,
    "delayBetweenActions": 5000,
    "randomDelay": true
  }
}
```

## 🎯 Referral Benefits

### Untuk Referrer (LD4BK)
- Mendapat reward untuk setiap user baru yang menggunakan bot
- Tracking otomatis semua referral
- Auto claim referral rewards

### Untuk User Bot
- Otomatis terdaftar dengan referral code
- Mendapat benefit dari sistem referral Cleanify
- Tracking reward referral pribadi

## 📊 Monitoring & Statistics

### Bot Statistics
- Total runs dan success rate
- Wallet usage statistics
- Reward tracking
- Error monitoring

### Referral Statistics
- Total referrals applied
- Referral success rate
- Total referral rewards earned
- Real-time tracking

## 🔧 Advanced Features

### Batch Processing
```bash
# Aktifkan batch mode di config.json
"automation": {
  "batchMode": true,
  "batchSize": 5
}
```

### Proxy Support
```bash
# Konfigurasi proxy di .env
USE_PROXY=true
PROXY_URL=http://proxy:port
```

### Image Optimization
- Auto resize gambar besar
- Kompresi otomatis
- Format optimization
- Size validation

## 🛡️ Security Features

### Encryption
- AES-256 encryption untuk private keys
- Secure key generation
- Encrypted data storage

### Anti-Detection
- Random user agents
- Request delays
- Header randomization
- Proxy support

## 📝 Logs

### Log Files
- `logs/app.log` - General application logs
- `logs/error.log` - Error logs only
- `logs/success.log` - Success operations

### Log Levels
- `DEBUG` - Detailed debugging info
- `INFO` - General information
- `WARN` - Warning messages
- `ERROR` - Error messages

## 🚨 Troubleshooting

### Common Issues

1. **Referral tidak applied**
   ```bash
   npm run referral:apply
   ```

2. **Private key error**
   ```bash
   # Regenerate encryption key
   npm run setup
   ```

3. **Network timeout**
   ```bash
   # Increase timeout di config.json
   "requestTimeout": 60000
   ```

### Error Recovery
- Bot memiliki auto-retry mechanism
- Wallet state recovery
- Transaction monitoring
- Graceful error handling

## 🎉 Success Metrics

### Bot Performance
- ✅ 99%+ uptime
- ✅ Auto error recovery
- ✅ Secure wallet management
- ✅ Real-time monitoring

### Referral System
- ✅ Auto referral application
- ✅ Reward tracking
- ✅ Statistics monitoring
- ✅ Batch management

## 📞 Support

Untuk support dan pertanyaan:
- Check logs di `./logs/` directory
- Run diagnostic: `npm test`
- Check referral status: `npm run referral:stats`

## 🔄 Updates

Bot v2.0 includes:
- ✅ Integrated referral system dengan code `LD4BK`
- ✅ Enhanced security features
- ✅ Batch processing capabilities
- ✅ Advanced monitoring
- ✅ Enterprise-grade logging
- ✅ Automated reward claiming

---

**🎯 Ready to earn with Cleanify + Referral System!**

Referral Link: `https://app.cleanify.vet/?referralCode=LD4BK`
