import fs from 'fs-extra';
import path from 'path';
import { Mnemonic, HDKey, Address } from '@vechain/sdk-core';
import { Wallet } from 'ethers';
import { Buffer } from 'node:buffer';
import logger from '../utils/logger.js';
import CryptoManager from '../utils/crypto.js';

class WalletManager {
    constructor(config, cryptoManager) {
        this.config = config;
        this.crypto = cryptoManager;
        this.accountsFile = config.files.accountsFile || './data/accounts.json';
        this.accounts = [];
        this.loadAccounts();
    }

    /**
     * Load existing accounts from file
     */
    async loadAccounts() {
        try {
            if (await fs.pathExists(this.accountsFile)) {
                const data = await fs.readJson(this.accountsFile);
                this.accounts = data.accounts || [];
                logger.success(`Loaded ${this.accounts.length} existing accounts`);
            } else {
                this.accounts = [];
                logger.progress('No existing accounts file found');
            }
        } catch (error) {
            logger.failure('Failed to load accounts', { error: error.message });
            this.accounts = [];
        }
    }

    /**
     * Save accounts to file
     */
    async saveAccounts() {
        try {
            await fs.ensureDir(path.dirname(this.accountsFile));
            
            const data = {
                version: '2.0.0',
                created: new Date().toISOString(),
                accounts: this.accounts
            };

            await fs.writeJson(this.accountsFile, data, { spaces: 2 });
            
            // Create backup
            if (this.config.wallet.backupEnabled) {
                const backupPath = this.accountsFile.replace('.json', `_backup_${Date.now()}.json`);
                await fs.writeJson(backupPath, data, { spaces: 2 });
                logger.success('Account backup created', { backupPath });
            }

            logger.success('Accounts saved successfully', { 
                count: this.accounts.length,
                file: this.accountsFile 
            });

        } catch (error) {
            logger.failure('Failed to save accounts', { error: error.message });
            throw error;
        }
    }

    /**
     * Create new wallet
     * @returns {Promise<Object>} - Wallet object
     */
    async createWallet() {
        try {
            logger.progress('Generating new wallet...');

            const words = Mnemonic.of();
            const hd = HDKey.fromMnemonic(words);
            const child = hd.deriveChild(0);
            const privKey = child.privateKey;
            const privHex = '0x' + Buffer.from(privKey).toString('hex');
            const pubKey = child.publicKey;
            const addr = Address.ofPublicKey(pubKey).toString();

            const wallet = {
                id: this.crypto.generateRandomString(16),
                address: addr,
                privateKey: privHex,
                privateKeyBytes: privKey,
                mnemonic: words.toString(),
                created: new Date().toISOString(),
                used: false,
                lastUsed: null,
                successCount: 0,
                failureCount: 0
            };

            logger.success('Wallet generated successfully', {
                id: wallet.id,
                address: wallet.address
            });

            return wallet;

        } catch (error) {
            logger.failure('Failed to create wallet', { error: error.message });
            throw error;
        }
    }

    /**
     * Get wallet for use (existing or new)
     * @returns {Promise<Object>} - Wallet object
     */
    async getWallet() {
        try {
            let wallet;

            // Try to reuse existing wallet if enabled
            if (this.config.wallet.reuseExisting && this.accounts.length > 0) {
                // Find unused wallet or least used one
                wallet = this.accounts.find(acc => !acc.used) || 
                         this.accounts.sort((a, b) => a.successCount - b.successCount)[0];
                
                if (wallet) {
                    logger.progress('Reusing existing wallet', { 
                        id: wallet.id,
                        address: wallet.address,
                        successCount: wallet.successCount
                    });
                }
            }

            // Create new wallet if none available
            if (!wallet) {
                wallet = await this.createWallet();
                this.accounts.push(wallet);
            }

            // Decrypt sensitive data if encrypted
            if (wallet.encrypted) {
                wallet.privateKey = this.crypto.decrypt(wallet.privateKey);
                wallet.mnemonic = this.crypto.decrypt(wallet.mnemonic);
            }

            return wallet;

        } catch (error) {
            logger.failure('Failed to get wallet', { error: error.message });
            throw error;
        }
    }

    /**
     * Save wallet after use
     * @param {Object} wallet - Wallet object
     * @param {boolean} success - Whether operation was successful
     */
    async saveWallet(wallet, success = true) {
        try {
            // Update wallet stats
            wallet.used = true;
            wallet.lastUsed = new Date().toISOString();
            
            if (success) {
                wallet.successCount = (wallet.successCount || 0) + 1;
            } else {
                wallet.failureCount = (wallet.failureCount || 0) + 1;
            }

            // Find and update wallet in accounts array
            const index = this.accounts.findIndex(acc => acc.id === wallet.id);
            if (index !== -1) {
                // Encrypt sensitive data before saving
                if (this.config.security.encryptPrivateKeys) {
                    const encryptedWallet = { ...wallet };
                    encryptedWallet.privateKey = this.crypto.encrypt(wallet.privateKey);
                    encryptedWallet.mnemonic = this.crypto.encrypt(wallet.mnemonic);
                    encryptedWallet.encrypted = true;
                    
                    this.accounts[index] = encryptedWallet;
                } else {
                    this.accounts[index] = wallet;
                }
            }

            // Save to file if enabled
            if (this.config.wallet.saveToFile) {
                await this.saveAccounts();
            }

            logger.success('Wallet saved successfully', {
                id: wallet.id,
                address: wallet.address,
                success,
                successCount: wallet.successCount,
                failureCount: wallet.failureCount
            });

        } catch (error) {
            logger.failure('Failed to save wallet', { error: error.message });
            throw error;
        }
    }

    /**
     * Get wallet statistics
     * @returns {Object} - Wallet statistics
     */
    getStats() {
        const stats = {
            totalWallets: this.accounts.length,
            usedWallets: this.accounts.filter(acc => acc.used).length,
            unusedWallets: this.accounts.filter(acc => !acc.used).length,
            totalSuccess: this.accounts.reduce((sum, acc) => sum + (acc.successCount || 0), 0),
            totalFailures: this.accounts.reduce((sum, acc) => sum + (acc.failureCount || 0), 0),
            successRate: 0
        };

        const totalOperations = stats.totalSuccess + stats.totalFailures;
        if (totalOperations > 0) {
            stats.successRate = ((stats.totalSuccess / totalOperations) * 100).toFixed(2);
        }

        return stats;
    }

    /**
     * Export wallets to backup file
     * @param {string} filePath - Export file path
     */
    async exportWallets(filePath) {
        try {
            const exportData = {
                version: '2.0.0',
                exported: new Date().toISOString(),
                accounts: this.accounts.map(acc => ({
                    ...acc,
                    // Remove sensitive data from export
                    privateKey: acc.encrypted ? '[ENCRYPTED]' : '[HIDDEN]',
                    mnemonic: acc.encrypted ? '[ENCRYPTED]' : '[HIDDEN]'
                })),
                stats: this.getStats()
            };

            await fs.writeJson(filePath, exportData, { spaces: 2 });
            logger.success('Wallets exported successfully', { filePath });

        } catch (error) {
            logger.failure('Failed to export wallets', { error: error.message });
            throw error;
        }
    }
}

export default WalletManager;
