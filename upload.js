import dotenv from 'dotenv';
import fs from 'fs-extra';
import path from 'path';
import FormData from 'form-data';
import { Address, Clause, ABIContract } from '@vechain/sdk-core';
import { Wallet } from 'ethers';
import { Buffer } from 'node:buffer';
import { ThorClient, ProviderInternalBaseWallet, VeChainProvider } from '@vechain/sdk-network';

// Import our enhanced utilities
import logger from './src/utils/logger.js';
import CryptoManager from './src/utils/crypto.js';
import NetworkManager from './src/utils/network.js';
import FileManager from './src/utils/fileManager.js';
import WalletManager from './src/wallet/walletManager.js';

// Load environment variables
dotenv.config();

// Load configuration
const config = await fs.readJson('./config.json');

// Initialize managers
const cryptoManager = new CryptoManager(process.env.ENCRYPTION_KEY);
const networkManager = new NetworkManager(config);
const fileManager = new FileManager(config);
const walletManager = new WalletManager(config, cryptoManager);

// Global variables
let currentWallet = null;
let currentCertificate = null;

// Smart contract ABI
const REWARD_CLAIMER_ABI = [
    {
        inputs: [
            {
                components: [
                    {
                        internalType: "enum RewardClaimer.RewardType",
                        name: "rewardType",
                        type: "uint8"
                    },
                    {
                        internalType: "uint256",
                        name: "amount",
                        type: "uint256"
                    },
                    {
                        internalType: "string[]",
                        name: "proofTypes",
                        type: "string[]"
                    },
                    {
                        internalType: "string[]",
                        name: "proofValues",
                        type: "string[]"
                    },
                    {
                        internalType: "string[]",
                        name: "impactCodes",
                        type: "string[]"
                    },
                    {
                        internalType: "uint256[]",
                        name: "impactValues",
                        type: "uint256[]"
                    },
                    {
                        internalType: "string",
                        name: "description",
                        type: "string"
                    },
                    {
                        internalType: "string",
                        name: "claimId",
                        type: "string"
                    },
                    {
                        internalType: "bytes",
                        name: "signature",
                        type: "bytes"
                    }
                ],
                internalType: "struct RewardClaimer.ClaimParams",
                name: "params",
                type: "tuple"
            }
        ],
        name: "claimReward",
        outputs: [],
        stateMutability: "nonpayable",
        type: "function"
    }
];

/**
 * Generate authentication certificate for wallet
 * @param {Object} wallet - Wallet object
 * @returns {Promise<string>} - Certificate string
 */

async function generateCertificate(wallet) {
    console.log('📝 Generating Authentication Certificate...');

    const value = {
        user: wallet.address.toLowerCase(),
        isSocialLogin: false,
        timestamp: new Date().toISOString()
    };

    const verifyingContract = '******************************************';

    const domain = {
        name: 'Cleanify',
        version: '1',
        chainId: 1,
        verifyingContract
    };
    const types = {
        Authentication: [
            { name: 'user', type: 'address' },
            { name: 'isSocialLogin', type: 'bool' },
            { name: 'timestamp', type: 'string' }
        ]
    };
    const signer = new Wallet(wallet.privateKey);
    const signature = await signer.signTypedData(domain, types, value);

    const certificate = { address: wallet.address, value, signature };
    const certB64 = Buffer.from(JSON.stringify(certificate)).toString('base64');

    console.log('✅ Certificate Generated Successfully');

    return `certificate ${certB64}`;
}

async function saveAccountToFile(wallet) {
    try {
        const accountData = `${wallet.address}|${wallet.mnemonic}|${wallet.privateKey}\n`;
        fs.appendFileSync('account.txt', accountData, 'utf8');
        console.log('💾 Account Saved to account.txt');
    } catch (error) {
        console.log('⚠️  Failed to save account to file:', error.message);
    }
}

async function checkUserExistence(wallet, certificate) {
    try {
        console.log('🔍 Checking User Existence...');

        const response = await fetch(CHECK_USER_API_URL, {
            method: 'GET',
            headers: {
                'x-wallet-address': wallet.address,
                'Authorization': certificate
            }
        });

        const result = await response.json();

        if (response.ok) {
            console.log('✅ User Already Exists');
            return { exists: true, user: result };
        } else if (result.message === 'User not found') {
            console.log('ℹ️  User Not Found - Creating New Account');
            return { exists: false, message: result.message };
        } else {
            console.log('⚠️  User Check Failed:', result.message || 'Unknown error');
            return { exists: false, error: result };
        }

    } catch (error) {
        console.log('❌ User Check Error:', error.message);
        return { exists: false, error: error.message };
    }
}

async function createAccount(wallet, certificate) {
    try {
        console.log('👤 Creating New Account...');

        const payload = {
            name: "",
            surname: ""
        };

        const response = await fetch(CREATE_USER_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-wallet-address': wallet.address,
                'Authorization': certificate
            },
            body: JSON.stringify(payload)
        });

        const result = await response.json();

        if (!response.ok) {
            if (response.status === 409) {
                console.log('ℹ️  Account Already Exists');
                return { success: true, message: 'Account already exists', created: false };
            } else {
                console.log('❌ Account Creation Failed:', result.message || 'Unknown error');
                return { success: false, error: result };
            }
        } else {
            console.log('✅ Account Created Successfully');
            await saveAccountToFile(wallet);
            return { success: true, result, created: true };
        }

    } catch (error) {
        console.log('❌ Account Creation Error:', error.message);
        throw error;
    }
}

async function setupWalletAndAccount() {
    try {
        console.log('🚀 Starting Wallet & Account Setup...\n');

        globalWallet = await createWallet();
        globalCertificate = await generateCertificate(globalWallet);

        const userCheckResult = await checkUserExistence(globalWallet, globalCertificate);

        if (userCheckResult.exists) {
            console.log('ℹ️  Skipping Account Creation - User Exists\n');
        } else {
            const accountResult = await createAccount(globalWallet, globalCertificate);
            console.log('');
        }

        return { wallet: globalWallet, certificate: globalCertificate, accountResult: userCheckResult };

    } catch (error) {
        console.log('❌ Setup Failed:', error.message);
        throw error;
    }
}

async function uploadImageToIPFS(imagePath) {
    try {
        console.log('📁 Preparing Image Upload...');

        if (!fs.existsSync(imagePath)) {
            throw new Error(`Image file not found: ${imagePath}`);
        }

        const form = new FormData();
        const imageStream = fs.createReadStream(imagePath);
        const filename = path.basename(imagePath);

        form.append('file', imageStream, {
            filename: filename,
            contentType: 'image/png'
        });

        console.log('⬆️  Uploading to IPFS...');

        const response = await fetch(IPFS_API_URL, {
            method: 'POST',
            headers: {
                'accept': '*/*',
                'accept-language': 'id-ID,id;q=0.9,en-US;q=0.8,en;q=0.7',
                'origin': 'https://app.cleanify.vet',
                'referer': 'https://app.cleanify.vet/',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?1',
                'sec-ch-ua-platform': '"Android"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'cross-site',
                'user-agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
                ...form.getHeaders()
            },
            body: form
        });

        if (!response.ok) {
            throw new Error(`IPFS upload failed: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        console.log('✅ IPFS Upload Successful');

        return result;

    } catch (error) {
        console.log('❌ IPFS Upload Failed:', error.message);
        throw error;
    }
}

async function submitDailyAction(ipfsHash) {
    try {
        console.log('📝 Submitting Daily Action...');

        const ipfsUrl = `https://api.gateway-proxy.vechain.org/ipfs/${ipfsHash}`;
        const timestamp = new Date().toISOString();

        const payload = {
            type: "butt-disposal",
            picture: {
                url: ipfsUrl,
                timestamp: timestamp
            }
        };

        const response = await fetch(DAILY_ACTION_API_URL, {
            method: 'POST',
            headers: {
                'Accept': '*/*',
                'Accept-Language': 'id-ID,id;q=0.9,en-US;q=0.8,en;q=0.7',
                'Authorization': globalCertificate,
                'x-wallet-address': globalWallet.address.toLowerCase(),
                'Connection': 'keep-alive',
                'Content-Type': 'application/json',
                'Origin': 'https://app.cleanify.vet',
                'Referer': 'https://app.cleanify.vet/',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-site',
                'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?1',
                'sec-ch-ua-platform': '"Android"'
            },
            body: JSON.stringify(payload)
        });

        const result = await response.json();

        if (!response.ok) {
            console.log('❌ Daily Action Failed:', result.message || 'Unknown error');
            return result;
        } else {
            console.log('✅ Daily Action Submitted Successfully');
            return result;
        }

    } catch (error) {
        console.log('❌ Daily Action Error:', error.message);
        throw error;
    }
}

async function getBlockRef() {
    try {
        const blk = await (await fetch(`${VECHAIN_NODE_URL}/blocks/best`)).json();
        return blk.id.slice(0, 18);              
    } catch {
        return '0x00000000aabbccdd';                  
    }
}

async function sendContractCall(claimData) {
    console.log('⛓️ Building clause …');
    const thor = ThorClient.fromUrl(VECHAIN_NODE_URL);

    const contract = ABIContract.ofAbi(REWARD_CLAIMER_ABI);

    const clause = Clause.callFunction(
        Address.of(REWARD_CLAIMER_CONTRACT),
        contract.getFunction('claimReward'),
        [{
            ...claimData.payload,
            signature: claimData.signature,
            claimId: claimData.payload.claimId
        }]
    );

    console.log('⛽ Estimating gas …');
    const gasResult = await thor.transactions.estimateGas([clause], globalWallet.address);

    console.log('📝 Building tx body …');
    const txBody = await thor.transactions.buildTransactionBody(
        [clause],
        gasResult.totalGas,
        {
            isDelegated: true,
            origin: globalWallet.address 
          }
    );

    console.log('🔑 Preparing signer …');
    const wallet = new ProviderInternalBaseWallet([
        { privateKey: globalWallet.privateKeyBytes, address: globalWallet.address },
    ], {
        gasPayer: {
            gasPayerServiceUrl: FEE_DELEGATION_URL
        },
    });

    const provider = new VeChainProvider(thor, wallet, true);
    const signer = await provider.getSigner(globalWallet.address);

    console.log('✍️ Signing tx …');
    const rawSignedTx = await signer.signTransaction(txBody, globalWallet.privateKey);

    console.log('📤 Broadcasting tx …');
    const broadcastRes = await fetch(`${VECHAIN_NODE_URL}/transactions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ raw: rawSignedTx }),
    });

    const out = await broadcastRes.json();
    if (!broadcastRes.ok) {
        console.error('❌ Broadcast failed:', out);
        return;
    }

    console.log('✅ TX sent →', out.id);

    console.log('⏳ Waiting for receipt …');
    const txReceipt = await thor.transactions.waitForTransaction(out.id);
    return txReceipt;
}

async function claimRewardAPI(actionId) {
    try {
        console.log('🎯 Finalizing Claim via API...');

        const claimUrl = `${DAILY_ACTION_API_URL}/${actionId}/claim`;

        const response = await fetch(claimUrl, {
            method: 'POST',
            headers: {
                'Host': 'graph.cleanify.vet',
                'Content-Type': 'application/json',
                'x-wallet-address': globalWallet.address.toLowerCase(),
                'Accept': '*/*',
                'Authorization': globalCertificate,
                'Sec-Fetch-Site': 'same-site',
                'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Sec-Fetch-Mode': 'cors',
                'Origin': 'https://app.cleanify.vet',
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                'Referer': 'https://app.cleanify.vet/',
                'Content-Length': '0',
                'Sec-Fetch-Dest': 'empty',
                'Connection': 'keep-alive'
            }
        });

        const result = await response.json();

        if (!response.ok) {
            console.log('❌ API Claim Failed:', result.message || 'Unknown error');
            return { success: false, error: result };
        } else {
            console.log('✅ API Claim Successful');
            console.log(`💎 Final Reward: ${result.data.tokenReward} VET`);
            return { success: true, result };
        }

    } catch (error) {
        console.log('❌ API Claim Error:', error.message);
        return { success: false, error: error.message };
    }
}

async function main() {
    try {
        console.log('🌟 CLEANIFY AUTO UPLOADER 🌟\n');
        console.log('========================================\n');

        const setupResult = await setupWalletAndAccount();
        console.log('========================================\n');

        console.log('📂 Starting Image Upload Process...');
        const ipfsResult = await uploadImageToIPFS(IMAGE_FILE);

        if (!ipfsResult.IpfsHash) {
            throw new Error('No IPFS hash received from upload');
        }

        console.log(`📋 IPFS Hash: ${ipfsResult.IpfsHash}`);
        console.log(`📏 File Size: ${ipfsResult.PinSize} bytes\n`);

        const actionResult = await submitDailyAction(ipfsResult.IpfsHash);

        if (actionResult.success && actionResult.data && actionResult.data.signature && actionResult.data.payload) {
            console.log(`📄 Action ID: ${actionResult.data.id}\n`);

            const contractResult = await sendContractCall(actionResult.data);

            if (contractResult.meta) {
                console.log('');
                const apiClaimResult = await claimRewardAPI(actionResult.data.id);

                console.log('\n========================================');
                console.log('🎯 PROCESS COMPLETED');
                console.log('========================================');
                console.log(`💳 Wallet: ${globalWallet.address}`);
                console.log(`🔐 Private Key: ${globalWallet.privateKey}`);
                console.log('✅ Daily Action: SUCCESS');
                console.log('✅ Contract Call: SUCCESS');
                console.log(`📄 TX ID: ${contractResult.txId}`);

                if (apiClaimResult.success) {
                    console.log('✅ API Claim: SUCCESS');
                    console.log(`💰 Final Reward: ${apiClaimResult.result.data.tokenReward} VET`);
                } else {
                    console.log('⚠️  API Claim: FAILED');
                }
            } else {
                console.log('\n========================================');
                console.log('🎯 PROCESS COMPLETED WITH ERRORS');
                console.log('========================================');
                console.log(`💳 Wallet: ${globalWallet.address}`);
                console.log(`🔐 Private Key: ${globalWallet.privateKey}`);
                console.log('✅ Daily Action: SUCCESS');
                console.log('❌ Contract Call: FAILED');
                console.log(`📝 Error: ${contractResult.error}`);
            }

        } else {
            console.log('\n========================================');
            console.log('🎯 PROCESS COMPLETED WITH WARNINGS');
            console.log('========================================');
            console.log(`💳 Wallet: ${globalWallet.address}`);
            console.log(`🔐 Private Key: ${globalWallet.privateKey}`);
            console.log('⚠️  Daily Action: COMPLETED BUT NO REWARD DATA');

            if (actionResult.message) {
                console.log(`📝 Message: ${actionResult.message}`);
            }
        }

        console.log('========================================');

    } catch (error) {
        console.log('\n========================================');
        console.log('💥 PROCESS FAILED');
        console.log('========================================');
        console.log(`❌ Error: ${error.message}`);
        console.log('========================================');
        process.exit(1);
    }
}

main(); 