{"app": {"name": "Cleanify Auto Upload Bot", "version": "2.0.0", "debug": false, "maxRetries": 3, "retryDelay": 2000, "requestTimeout": 30000}, "security": {"encryptPrivateKeys": true, "encryptionKey": "your-secret-key-here-change-this", "randomizeUserAgent": true, "useProxy": false, "proxyUrl": ""}, "api": {"ipfsUrl": "https://api.gateway-proxy.vechain.org/api/v1/pinning/pinFileToIPFS", "dailyActionUrl": "https://graph.cleanify.vet/api/v1/daily-actions", "createUserUrl": "https://graph.cleanify.vet/api/v1/cleanify-user/create", "checkUserUrl": "https://graph.cleanify.vet/api/v1/cleanify-user/me", "referralUrl": "https://graph.cleanify.vet/api/v1/referrals", "vchainNodeUrl": "https://mainnet.vechain.org", "feeDelegationUrl": "https://sponsor.vechain.energy/by/749"}, "contract": {"rewardClaimerAddress": "******************************************", "verifyingContract": "******************************************"}, "files": {"imageDirectory": "./images", "accountsFile": "./data/accounts.json", "logsDirectory": "./logs", "supportedFormats": ["png", "jpg", "jpeg", "gif", "webp"], "maxFileSize": ********, "imageQuality": 80}, "automation": {"batchMode": false, "batchSize": 5, "delayBetweenActions": 5000, "randomDelay": true, "maxRandomDelay": 10000}, "wallet": {"reuseExisting": false, "saveToFile": true, "backupEnabled": true}, "referral": {"enabled": true, "referralCode": "LD4BK", "autoApplyReferral": true, "trackReferralRewards": true, "referralRewardThreshold": 10}}