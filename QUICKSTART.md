# 🚀 Quick Start Guide - Cleanify Bot v2.0

## 🎯 Referral System Terintegrasi
**Referral Code: `LD4BK`**  
**Referral Link: `https://app.cleanify.vet/?referralCode=LD4BK`**

---

## ⚡ Quick Setup (5 Menit)

### 1. Install Dependencies
```bash
npm install
```

### 2. Setup Bot
```bash
npm run setup
```

### 3. Add Images
```bash
# Letakkan gambar di folder ./images/
# atau gunakan image.png di root (legacy)
```

### 4. Run Bot
```bash
npm start
```

---

## 🎯 Referral Commands

```bash
# Lihat info referral
npm run referral:info

# Cek statistik referral
npm run referral:stats

# Cek reward yang bisa di-claim
npm run referral:check

# Claim referral rewards
npm run referral:claim

# Apply referral ke wallet existing
npm run referral:apply
```

---

## 📊 Fitur Utama v2.0

### ✅ Yang Sudah Diupdate:
- **Auto Referral System** dengan code `LD4BK`
- **Enhanced Security** dengan enkripsi AES-256
- **Batch Processing** untuk multiple gambar
- **Advanced Logging** dengan file rotation
- **Error Recovery** dengan retry mechanism
- **Wallet Management** yang aman
- **Real-time Statistics** tracking
- **Image Optimization** otomatis
- **Proxy Support** untuk anonymity
- **Configuration Management** yang flexible

### 🎯 Referral Benefits:
- **Auto Apply** referral untuk akun baru
- **Reward Tracking** otomatis
- **Batch Referral** management
- **Statistics** real-time
- **Auto Claim** referral rewards

---

## 🔧 Configuration Cepat

### Edit `.env` (Optional):
```env
REFERRAL_CODE=LD4BK
AUTO_APPLY_REFERRAL=true
TRACK_REFERRAL_REWARDS=true
```

### Edit `config.json` (Optional):
```json
{
  "referral": {
    "enabled": true,
    "referralCode": "LD4BK",
    "autoApplyReferral": true
  }
}
```

---

## 📁 File Structure Baru

```
├── src/                    # Core utilities
│   ├── utils/             # Logger, crypto, network
│   ├── wallet/            # Wallet management
│   └── referral/          # Referral system
├── data/                  # Encrypted data storage
├── images/                # Gambar untuk upload
├── logs/                  # Log files
├── upload.js             # Main bot script
├── referral.js           # Referral management
└── setup.js              # Setup script
```

---

## 🎉 Success Indicators

### ✅ Bot Running Successfully:
```
🌟 CLEANIFY AUTO UPLOADER v2.0 🌟
✅ Bot initialized successfully
🎯 Referral System: ENABLED
   • Referral Code: LD4BK
   • Total Referrals: X
   • Total Rewards: X VET
```

### ✅ Referral Working:
```
🎯 Applying referral code: LD4BK
✅ Referral applied successfully
💰 Referral rewards claimed: X VET
```

---

## 🚨 Troubleshooting

### Problem: Referral tidak applied
```bash
npm run referral:apply
```

### Problem: Bot error
```bash
# Check logs
cat logs/error.log

# Run tests
npm test
```

### Problem: No images found
```bash
# Add images to ./images/ folder
# or place image.png in root
```

---

## 📈 Monitoring

### Check Bot Stats:
```bash
npm start
# Lihat output statistics di akhir
```

### Check Referral Stats:
```bash
npm run referral:stats
```

### Check Logs:
```bash
# Error logs
cat logs/error.log

# Success logs  
cat logs/success.log

# All logs
cat logs/app.log
```

---

## 💡 Pro Tips

### 1. Batch Mode
```json
// config.json
"automation": {
  "batchMode": true,
  "batchSize": 5
}
```

### 2. Auto Retry
```json
// config.json  
"app": {
  "maxRetries": 3,
  "retryDelay": 2000
}
```

### 3. Proxy Usage
```env
// .env
USE_PROXY=true
PROXY_URL=http://proxy:port
```

---

## 🎯 Expected Results

### Per Run:
- ✅ Wallet generated/loaded
- ✅ Account created (with referral applied)
- ✅ Image uploaded to IPFS
- ✅ Daily action submitted
- ✅ Reward claimed
- ✅ Referral rewards checked/claimed

### Statistics:
- 📊 Success rate tracking
- 💰 Total rewards earned
- 🎯 Referral performance
- 👛 Wallet usage stats

---

## 🔗 Important Links

- **Referral Link**: `https://app.cleanify.vet/?referralCode=LD4BK`
- **Cleanify App**: `https://app.cleanify.vet/`
- **VeChain Explorer**: `https://explore.vechain.org/`

---

## ⚡ One-Liner Setup

```bash
npm install && npm run setup && npm start
```

---

**🎉 Ready to earn with enhanced Cleanify Bot + Referral System!**

**Referral Code: `LD4BK`** 🎯
