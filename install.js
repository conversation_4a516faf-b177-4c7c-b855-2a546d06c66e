#!/usr/bin/env node

/**
 * 🚀 CLEANIFY BOT v2.0 INSTALLER
 * 
 * Script untuk install dan setup bot dengan sistem referral
 * Referral Code: LD4BK
 * Referral Link: https://app.cleanify.vet/?referralCode=LD4BK
 */

import { execSync } from 'child_process';
import fs from 'fs-extra';
import path from 'path';

console.log('🌟 CLEANIFY AUTO UPLOAD BOT v2.0 INSTALLER 🌟');
console.log('===============================================');
console.log('🎯 Referral Code: LD4BK');
console.log('🔗 Referral Link: https://app.cleanify.vet/?referralCode=LD4BK');
console.log('===============================================\n');

async function install() {
    try {
        console.log('📦 Installing dependencies...');
        execSync('npm install', { stdio: 'inherit' });
        
        console.log('\n🔧 Running setup...');
        execSync('node setup.js', { stdio: 'inherit' });
        
        console.log('\n🧪 Running tests...');
        try {
            execSync('npm test', { stdio: 'inherit' });
        } catch (error) {
            console.log('⚠️  Some tests failed, but installation can continue');
        }
        
        console.log('\n✅ Installation completed successfully!');
        console.log('\n🎯 REFERRAL SYSTEM CONFIGURED:');
        console.log('   • Referral Code: LD4BK');
        console.log('   • Auto Apply: ENABLED');
        console.log('   • Reward Tracking: ENABLED');
        
        console.log('\n📋 NEXT STEPS:');
        console.log('1. Add images to ./images/ directory');
        console.log('2. Run: npm start');
        console.log('3. Check referral stats: npm run referral:stats');
        
        console.log('\n🎉 Ready to earn with Cleanify + Referral rewards!');
        
    } catch (error) {
        console.error('❌ Installation failed:', error.message);
        process.exit(1);
    }
}

install();
