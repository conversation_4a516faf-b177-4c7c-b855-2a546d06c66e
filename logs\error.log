{"error":"Request failed with status code 404","level":"error","message":"❌ FAILURE: Request attempt 1 failed","status":404,"timestamp":"2025-07-15T14:29:09.920Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/me"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"❌ FAILURE: Request attempt 1 failed","timestamp":"2025-07-15T14:29:39.939Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/create"}
{"error":"Request failed with status code 400","level":"error","message":"❌ FAILURE: Request attempt 2 failed","status":400,"timestamp":"2025-07-15T14:29:50.144Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/create"}
{"error":"Request failed with status code 400","level":"error","message":"❌ FAILURE: Account creation failed","timestamp":"2025-07-15T14:29:50.146Z"}
{"error":"Request failed with status code 400","level":"error","message":"❌ FAILURE: Setup failed","timestamp":"2025-07-15T14:29:50.147Z"}
{"duration":"50.82s","error":"Request failed with status code 400","image":"image.png","level":"error","message":"❌ FAILURE: Image processing failed","timestamp":"2025-07-15T14:29:50.148Z"}
{"level":"error","message":"❌ FAILURE: ❌ Failed Images:","timestamp":"2025-07-15T14:29:50.175Z"}
{"level":"error","message":"❌ FAILURE:    • image.png","timestamp":"2025-07-15T14:29:50.175Z"}
{"error":"Request failed with status code 404","level":"error","message":"❌ FAILURE: Request attempt 1 failed","status":404,"timestamp":"2025-07-15T14:31:01.765Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/me"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"❌ FAILURE: Request attempt 1 failed","timestamp":"2025-07-15T14:31:31.777Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/create"}
{"error":"Request failed with status code 400","level":"error","message":"❌ FAILURE: Request attempt 2 failed","status":400,"timestamp":"2025-07-15T14:31:38.045Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/create"}
{"error":"Request failed with status code 400","level":"error","message":"❌ FAILURE: Account creation failed","timestamp":"2025-07-15T14:31:38.045Z"}
{"error":"Request failed with status code 400","level":"error","message":"❌ FAILURE: Setup failed","timestamp":"2025-07-15T14:31:38.045Z"}
{"duration":"40.72s","error":"Request failed with status code 400","image":"image.png","level":"error","message":"❌ FAILURE: Image processing failed","timestamp":"2025-07-15T14:31:38.046Z"}
{"level":"error","message":"❌ FAILURE: ❌ Failed Images:","timestamp":"2025-07-15T14:31:38.066Z"}
{"level":"error","message":"❌ FAILURE:    • image.png","timestamp":"2025-07-15T14:31:38.067Z"}
{"level":"error","message":"❌ FAILURE: No wallets found","timestamp":"2025-07-15T14:32:40.234Z"}
{"level":"error","message":"❌ FAILURE: No wallets found","timestamp":"2025-07-15T14:32:51.274Z"}
{"level":"error","message":"❌ FAILURE: No wallets found","timestamp":"2025-07-15T14:32:54.214Z"}
