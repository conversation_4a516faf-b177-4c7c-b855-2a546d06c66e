import CryptoJS from 'crypto-js';
import logger from './logger.js';

class CryptoManager {
    constructor(encryptionKey) {
        this.encryptionKey = encryptionKey || process.env.ENCRYPTION_KEY;
        if (!this.encryptionKey) {
            throw new Error('Encryption key is required');
        }
    }

    /**
     * Encrypt sensitive data
     * @param {string} data - Data to encrypt
     * @returns {string} - Encrypted data
     */
    encrypt(data) {
        try {
            const encrypted = CryptoJS.AES.encrypt(data, this.encryptionKey).toString();
            logger.security('Data encrypted successfully');
            return encrypted;
        } catch (error) {
            logger.failure('Failed to encrypt data', { error: error.message });
            throw error;
        }
    }

    /**
     * Decrypt sensitive data
     * @param {string} encryptedData - Encrypted data
     * @returns {string} - Decrypted data
     */
    decrypt(encryptedData) {
        try {
            const bytes = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey);
            const decrypted = bytes.toString(CryptoJS.enc.Utf8);
            
            if (!decrypted) {
                throw new Error('Failed to decrypt data - invalid key or corrupted data');
            }
            
            logger.security('Data decrypted successfully');
            return decrypted;
        } catch (error) {
            logger.failure('Failed to decrypt data', { error: error.message });
            throw error;
        }
    }

    /**
     * Generate a secure random string
     * @param {number} length - Length of random string
     * @returns {string} - Random string
     */
    generateRandomString(length = 32) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * Hash data using SHA256
     * @param {string} data - Data to hash
     * @returns {string} - Hashed data
     */
    hash(data) {
        return CryptoJS.SHA256(data).toString();
    }

    /**
     * Verify hash
     * @param {string} data - Original data
     * @param {string} hash - Hash to verify against
     * @returns {boolean} - Verification result
     */
    verifyHash(data, hash) {
        return this.hash(data) === hash;
    }
}

export default CryptoManager;
