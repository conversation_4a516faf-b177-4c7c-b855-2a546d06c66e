{"level":"info","message":"⏳ PROGRESS: 🎯 Initializing Referral Manager...","timestamp":"2025-07-15T14:28:30.795Z"}
{"level":"info","message":"✅ SUCCESS: Referral manager initialized successfully","timestamp":"2025-07-15T14:28:30.799Z"}
{"level":"info","message":"✅ SUCCESS: 🎯 CLEANIFY REFERRAL SYSTEM 🎯","timestamp":"2025-07-15T14:28:30.800Z"}
{"level":"info","message":"⏳ PROGRESS: =====================================","timestamp":"2025-07-15T14:28:30.800Z"}
{"level":"info","message":"⏳ PROGRESS: 🎯 Referral System Information:","timestamp":"2025-07-15T14:28:30.801Z"}
{"level":"info","message":"⏳ PROGRESS:    • Referral Code: LD4BK","timestamp":"2025-07-15T14:28:30.801Z"}
{"level":"info","message":"⏳ PROGRESS:    • Referral Link: https://app.cleanify.vet/?referralCode=LD4BK","timestamp":"2025-07-15T14:28:30.801Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Referrals: 0","timestamp":"2025-07-15T14:28:30.801Z"}
{"level":"info","message":"⏳ PROGRESS:    • Successful: 0","timestamp":"2025-07-15T14:28:30.802Z"}
{"level":"info","message":"⏳ PROGRESS:    • Failed: 0","timestamp":"2025-07-15T14:28:30.802Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Rewards: 0 VET","timestamp":"2025-07-15T14:28:30.802Z"}
{"level":"info","message":"⏳ PROGRESS:    • Success Rate: 0%","timestamp":"2025-07-15T14:28:30.802Z"}
{"level":"info","message":"⏳ PROGRESS: =====================================","timestamp":"2025-07-15T14:28:30.802Z"}
{"level":"info","message":"⏳ PROGRESS: 📋 Available Commands:","timestamp":"2025-07-15T14:28:30.802Z"}
{"level":"info","message":"⏳ PROGRESS:    • node referral.js info     - Show referral information","timestamp":"2025-07-15T14:28:30.802Z"}
{"level":"info","message":"⏳ PROGRESS:    • node referral.js stats    - Show detailed statistics","timestamp":"2025-07-15T14:28:30.803Z"}
{"level":"info","message":"⏳ PROGRESS:    • node referral.js check    - Check referral rewards","timestamp":"2025-07-15T14:28:30.803Z"}
{"level":"info","message":"⏳ PROGRESS:    • node referral.js claim    - Claim referral rewards","timestamp":"2025-07-15T14:28:30.803Z"}
{"level":"info","message":"⏳ PROGRESS:    • node referral.js apply    - Apply referral to existing wallets","timestamp":"2025-07-15T14:28:30.803Z"}
{"level":"info","message":"⏳ PROGRESS: =====================================","timestamp":"2025-07-15T14:28:30.803Z"}
{"level":"info","message":"⏳ PROGRESS: No existing accounts file found","timestamp":"2025-07-15T14:28:30.803Z"}
{"level":"info","message":"⏳ PROGRESS: 🚀 Initializing Cleanify Bot v2.0...","timestamp":"2025-07-15T14:28:59.311Z"}
{"level":"info","message":"✅ SUCCESS: Bot initialized successfully","timestamp":"2025-07-15T14:28:59.315Z"}
{"level":"info","message":"⏳ PROGRESS: 📋 Current Configuration:","timestamp":"2025-07-15T14:28:59.316Z"}
{"level":"info","message":"⏳ PROGRESS:    • Debug Mode: false","timestamp":"2025-07-15T14:28:59.316Z"}
{"level":"info","message":"⏳ PROGRESS:    • Max Retries: 3","timestamp":"2025-07-15T14:28:59.316Z"}
{"level":"info","message":"⏳ PROGRESS:    • Encrypt Keys: true","timestamp":"2025-07-15T14:28:59.316Z"}
{"level":"info","message":"⏳ PROGRESS:    • Random UA: true","timestamp":"2025-07-15T14:28:59.317Z"}
{"level":"info","message":"⏳ PROGRESS:    • Use Proxy: false","timestamp":"2025-07-15T14:28:59.317Z"}
{"level":"info","message":"⏳ PROGRESS:    • Batch Mode: false","timestamp":"2025-07-15T14:28:59.317Z"}
{"level":"info","message":"⏳ PROGRESS:    • Reuse Wallet: false","timestamp":"2025-07-15T14:28:59.318Z"}
{"level":"info","message":"⏳ PROGRESS:    • Referral System: ENABLED","timestamp":"2025-07-15T14:28:59.318Z"}
{"level":"info","message":"⏳ PROGRESS: 🎯 Referral System Information:","timestamp":"2025-07-15T14:28:59.318Z"}
{"level":"info","message":"⏳ PROGRESS:    • Referral Code: LD4BK","timestamp":"2025-07-15T14:28:59.318Z"}
{"level":"info","message":"⏳ PROGRESS:    • Referral Link: https://app.cleanify.vet/?referralCode=LD4BK","timestamp":"2025-07-15T14:28:59.318Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Referrals: 0","timestamp":"2025-07-15T14:28:59.318Z"}
{"level":"info","message":"⏳ PROGRESS:    • Successful: 0","timestamp":"2025-07-15T14:28:59.318Z"}
{"level":"info","message":"⏳ PROGRESS:    • Failed: 0","timestamp":"2025-07-15T14:28:59.318Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Rewards: 0 VET","timestamp":"2025-07-15T14:28:59.318Z"}
{"level":"info","message":"⏳ PROGRESS:    • Success Rate: 0%","timestamp":"2025-07-15T14:28:59.319Z"}
{"level":"info","message":"✅ SUCCESS: 🌟 CLEANIFY AUTO UPLOADER v2.0 🌟","timestamp":"2025-07-15T14:28:59.319Z"}
{"level":"info","message":"⏳ PROGRESS: ========================================","timestamp":"2025-07-15T14:28:59.319Z"}
{"level":"info","message":"⏳ PROGRESS: No existing accounts file found","timestamp":"2025-07-15T14:28:59.319Z"}
{"level":"info","message":"✅ SUCCESS: Directories initialized","timestamp":"2025-07-15T14:28:59.321Z"}
{"channels":3,"format":"jpeg","height":640,"level":"info","message":"✅ SUCCESS: Image validation passed","path":"images\\image.png","size":242422,"timestamp":"2025-07-15T14:28:59.323Z","valid":true,"width":967}
{"directory":"./images","level":"info","message":"✅ SUCCESS: Found 1 valid images","timestamp":"2025-07-15T14:28:59.323Z"}
{"level":"info","message":"⏳ PROGRESS: Found 1 image(s) to process","timestamp":"2025-07-15T14:28:59.323Z"}
{"level":"info","message":"⏳ PROGRESS: 🖼️ Processing image: image.png","timestamp":"2025-07-15T14:28:59.323Z"}
{"level":"info","message":"⏳ PROGRESS: 🔧 Setting up wallet and account...","timestamp":"2025-07-15T14:28:59.323Z"}
{"level":"info","message":"⏳ PROGRESS: Generating new wallet...","timestamp":"2025-07-15T14:28:59.324Z"}
{"address":"******************************************","id":"aM2CTgAoJlq2nnld","level":"info","message":"✅ SUCCESS: Wallet generated successfully","timestamp":"2025-07-15T14:28:59.397Z"}
{"level":"info","message":"⏳ PROGRESS: 📝 Generating authentication certificate...","timestamp":"2025-07-15T14:28:59.398Z"}
{"level":"info","message":"✅ SUCCESS: Certificate generated successfully","timestamp":"2025-07-15T14:28:59.438Z"}
{"level":"info","message":"⏳ PROGRESS: 🔍 Checking user existence...","timestamp":"2025-07-15T14:28:59.438Z"}
{"level":"info","message":"⏳ PROGRESS: Request attempt 1/3","method":"GET","timestamp":"2025-07-15T14:28:59.439Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/me"}
{"error":"Request failed with status code 404","level":"error","message":"❌ FAILURE: Request attempt 1 failed","status":404,"timestamp":"2025-07-15T14:29:09.920Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/me"}
{"level":"info","message":"⏳ PROGRESS: User not found - will create new account","timestamp":"2025-07-15T14:29:09.921Z"}
{"level":"info","message":"⏳ PROGRESS: 👤 Creating new account...","timestamp":"2025-07-15T14:29:09.921Z"}
{"level":"info","message":"⏳ PROGRESS: Request attempt 1/3","method":"POST","timestamp":"2025-07-15T14:29:09.921Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/create"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"❌ FAILURE: Request attempt 1 failed","timestamp":"2025-07-15T14:29:39.939Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/create"}
{"level":"info","message":"⏳ PROGRESS: Waiting 2448.2019733697757ms before retry...","timestamp":"2025-07-15T14:29:39.940Z"}
{"level":"info","message":"⏳ PROGRESS: Request attempt 2/3","method":"POST","timestamp":"2025-07-15T14:29:42.402Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/create"}
{"error":"Request failed with status code 400","level":"error","message":"❌ FAILURE: Request attempt 2 failed","status":400,"timestamp":"2025-07-15T14:29:50.145Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/create"}
{"error":"Request failed with status code 400","level":"error","message":"❌ FAILURE: Account creation failed","timestamp":"2025-07-15T14:29:50.146Z"}
{"error":"Request failed with status code 400","level":"error","message":"❌ FAILURE: Setup failed","timestamp":"2025-07-15T14:29:50.147Z"}
{"duration":"50.82s","error":"Request failed with status code 400","image":"image.png","level":"error","message":"❌ FAILURE: Image processing failed","timestamp":"2025-07-15T14:29:50.148Z"}
{"level":"warn","message":"🔒 SECURITY: Data encrypted successfully","timestamp":"2025-07-15T14:29:50.154Z"}
{"level":"warn","message":"🔒 SECURITY: Data encrypted successfully","timestamp":"2025-07-15T14:29:50.155Z"}
{"backupPath":"./data/accounts_backup_1752589790157.json","level":"info","message":"✅ SUCCESS: Account backup created","timestamp":"2025-07-15T14:29:50.159Z"}
{"count":1,"file":"./data/accounts.json","level":"info","message":"✅ SUCCESS: Accounts saved successfully","timestamp":"2025-07-15T14:29:50.160Z"}
{"address":"******************************************","failureCount":1,"id":"aM2CTgAoJlq2nnld","level":"info","message":"✅ SUCCESS: Wallet saved successfully","success":false,"successCount":0,"timestamp":"2025-07-15T14:29:50.161Z"}
{"level":"info","message":"⏳ PROGRESS: ========================================","timestamp":"2025-07-15T14:29:50.163Z"}
{"level":"info","message":"✅ SUCCESS: 🎯 EXECUTION COMPLETED","timestamp":"2025-07-15T14:29:50.163Z"}
{"level":"info","message":"⏳ PROGRESS: ========================================","timestamp":"2025-07-15T14:29:50.164Z"}
{"level":"info","message":"⏳ PROGRESS: 📊 Results Summary:","timestamp":"2025-07-15T14:29:50.165Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Images: 1","timestamp":"2025-07-15T14:29:50.166Z"}
{"level":"info","message":"⏳ PROGRESS:    • Successful: 0","timestamp":"2025-07-15T14:29:50.167Z"}
{"level":"info","message":"⏳ PROGRESS:    • Failed: 1","timestamp":"2025-07-15T14:29:50.167Z"}
{"level":"info","message":"⏳ PROGRESS:    • Success Rate: 0.0%","timestamp":"2025-07-15T14:29:50.168Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Rewards: 0 VET","timestamp":"2025-07-15T14:29:50.169Z"}
{"level":"info","message":"⏳ PROGRESS: 📈 Wallet Statistics:","timestamp":"2025-07-15T14:29:50.170Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Wallets: 1","timestamp":"2025-07-15T14:29:50.171Z"}
{"level":"info","message":"⏳ PROGRESS:    • Used Wallets: 1","timestamp":"2025-07-15T14:29:50.171Z"}
{"level":"info","message":"⏳ PROGRESS:    • Overall Success Rate: 0.00%","timestamp":"2025-07-15T14:29:50.172Z"}
{"level":"info","message":"⏳ PROGRESS: 🎯 Referral Statistics:","timestamp":"2025-07-15T14:29:50.172Z"}
{"level":"info","message":"⏳ PROGRESS:    • Referral Code: LD4BK","timestamp":"2025-07-15T14:29:50.173Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Referrals: 0","timestamp":"2025-07-15T14:29:50.173Z"}
{"level":"info","message":"⏳ PROGRESS:    • Successful: 0","timestamp":"2025-07-15T14:29:50.173Z"}
{"level":"info","message":"⏳ PROGRESS:    • Referral Success Rate: 0%","timestamp":"2025-07-15T14:29:50.174Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Referral Rewards: 0 VET","timestamp":"2025-07-15T14:29:50.174Z"}
{"level":"error","message":"❌ FAILURE: ❌ Failed Images:","timestamp":"2025-07-15T14:29:50.175Z"}
{"level":"error","message":"❌ FAILURE:    • image.png","timestamp":"2025-07-15T14:29:50.175Z"}
{"level":"info","message":"⏳ PROGRESS: ========================================","timestamp":"2025-07-15T14:29:50.176Z"}
{"file":"./data/bot_stats.json","level":"info","message":"✅ SUCCESS: Statistics saved","timestamp":"2025-07-15T14:29:50.177Z"}
{"level":"info","message":"⏳ PROGRESS: 🎯 Initializing Referral Manager...","timestamp":"2025-07-15T14:30:19.540Z"}
{"level":"info","message":"✅ SUCCESS: Referral manager initialized successfully","timestamp":"2025-07-15T14:30:19.544Z"}
{"level":"info","message":"✅ SUCCESS: 📊 REFERRAL STATISTICS","timestamp":"2025-07-15T14:30:19.544Z"}
{"level":"info","message":"⏳ PROGRESS: =======================","timestamp":"2025-07-15T14:30:19.545Z"}
{"level":"info","message":"⏳ PROGRESS: 🎯 Referral Code: LD4BK","timestamp":"2025-07-15T14:30:19.545Z"}
{"level":"info","message":"⏳ PROGRESS: 🔗 Referral Link: https://app.cleanify.vet/?referralCode=LD4BK","timestamp":"2025-07-15T14:30:19.545Z"}
{"level":"info","message":"⏳ PROGRESS: 📈 Total Referrals: 0","timestamp":"2025-07-15T14:30:19.546Z"}
{"level":"info","message":"⏳ PROGRESS: ✅ Successful: 0","timestamp":"2025-07-15T14:30:19.546Z"}
{"level":"info","message":"⏳ PROGRESS: ❌ Failed: 0","timestamp":"2025-07-15T14:30:19.546Z"}
{"level":"info","message":"⏳ PROGRESS: 📊 Success Rate: 0%","timestamp":"2025-07-15T14:30:19.546Z"}
{"level":"info","message":"⏳ PROGRESS: 💰 Total Rewards: 0 VET","timestamp":"2025-07-15T14:30:19.547Z"}
{"level":"info","message":"⏳ PROGRESS: 🕒 Last Updated: Never","timestamp":"2025-07-15T14:30:19.547Z"}
{"level":"info","message":"⏳ PROGRESS: \n👛 Wallet Statistics:","timestamp":"2025-07-15T14:30:19.547Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Wallets: 0","timestamp":"2025-07-15T14:30:19.547Z"}
{"level":"info","message":"⏳ PROGRESS:    • Used Wallets: 0","timestamp":"2025-07-15T14:30:19.547Z"}
{"level":"info","message":"⏳ PROGRESS:    • Unused Wallets: 0","timestamp":"2025-07-15T14:30:19.548Z"}
{"level":"info","message":"✅ SUCCESS: Loaded 1 existing accounts","timestamp":"2025-07-15T14:30:19.549Z"}
{"level":"info","message":"⏳ PROGRESS: 🚀 Initializing Cleanify Bot v2.0...","timestamp":"2025-07-15T14:30:57.316Z"}
{"level":"info","message":"✅ SUCCESS: Bot initialized successfully","timestamp":"2025-07-15T14:30:57.320Z"}
{"level":"info","message":"⏳ PROGRESS: 📋 Current Configuration:","timestamp":"2025-07-15T14:30:57.321Z"}
{"level":"info","message":"⏳ PROGRESS:    • Debug Mode: false","timestamp":"2025-07-15T14:30:57.321Z"}
{"level":"info","message":"⏳ PROGRESS:    • Max Retries: 3","timestamp":"2025-07-15T14:30:57.321Z"}
{"level":"info","message":"⏳ PROGRESS:    • Encrypt Keys: true","timestamp":"2025-07-15T14:30:57.322Z"}
{"level":"info","message":"⏳ PROGRESS:    • Random UA: true","timestamp":"2025-07-15T14:30:57.322Z"}
{"level":"info","message":"⏳ PROGRESS:    • Use Proxy: false","timestamp":"2025-07-15T14:30:57.322Z"}
{"level":"info","message":"⏳ PROGRESS:    • Batch Mode: false","timestamp":"2025-07-15T14:30:57.323Z"}
{"level":"info","message":"⏳ PROGRESS:    • Reuse Wallet: false","timestamp":"2025-07-15T14:30:57.323Z"}
{"level":"info","message":"⏳ PROGRESS:    • Referral System: ENABLED","timestamp":"2025-07-15T14:30:57.323Z"}
{"level":"info","message":"⏳ PROGRESS: 🎯 Referral System Information:","timestamp":"2025-07-15T14:30:57.323Z"}
{"level":"info","message":"⏳ PROGRESS:    • Referral Code: LD4BK","timestamp":"2025-07-15T14:30:57.323Z"}
{"level":"info","message":"⏳ PROGRESS:    • Referral Link: https://app.cleanify.vet/?referralCode=LD4BK","timestamp":"2025-07-15T14:30:57.323Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Referrals: 0","timestamp":"2025-07-15T14:30:57.324Z"}
{"level":"info","message":"⏳ PROGRESS:    • Successful: 0","timestamp":"2025-07-15T14:30:57.324Z"}
{"level":"info","message":"⏳ PROGRESS:    • Failed: 0","timestamp":"2025-07-15T14:30:57.324Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Rewards: 0 VET","timestamp":"2025-07-15T14:30:57.324Z"}
{"level":"info","message":"⏳ PROGRESS:    • Success Rate: 0%","timestamp":"2025-07-15T14:30:57.324Z"}
{"level":"info","message":"✅ SUCCESS: 🌟 CLEANIFY AUTO UPLOADER v2.0 🌟","timestamp":"2025-07-15T14:30:57.325Z"}
{"level":"info","message":"⏳ PROGRESS: ========================================","timestamp":"2025-07-15T14:30:57.325Z"}
{"level":"info","message":"✅ SUCCESS: Loaded 1 existing accounts","timestamp":"2025-07-15T14:30:57.326Z"}
{"level":"info","message":"✅ SUCCESS: Directories initialized","timestamp":"2025-07-15T14:30:57.327Z"}
{"channels":3,"format":"jpeg","height":640,"level":"info","message":"✅ SUCCESS: Image validation passed","path":"images\\image.png","size":242422,"timestamp":"2025-07-15T14:30:57.328Z","valid":true,"width":967}
{"directory":"./images","level":"info","message":"✅ SUCCESS: Found 1 valid images","timestamp":"2025-07-15T14:30:57.329Z"}
{"level":"info","message":"⏳ PROGRESS: Found 1 image(s) to process","timestamp":"2025-07-15T14:30:57.329Z"}
{"level":"info","message":"⏳ PROGRESS: 🖼️ Processing image: image.png","timestamp":"2025-07-15T14:30:57.329Z"}
{"level":"info","message":"⏳ PROGRESS: 🔧 Setting up wallet and account...","timestamp":"2025-07-15T14:30:57.329Z"}
{"level":"info","message":"⏳ PROGRESS: Generating new wallet...","timestamp":"2025-07-15T14:30:57.330Z"}
{"address":"******************************************","id":"DTTT0dWzku7TbhUS","level":"info","message":"✅ SUCCESS: Wallet generated successfully","timestamp":"2025-07-15T14:30:57.414Z"}
{"level":"info","message":"⏳ PROGRESS: 📝 Generating authentication certificate...","timestamp":"2025-07-15T14:30:57.415Z"}
{"level":"info","message":"✅ SUCCESS: Certificate generated successfully","timestamp":"2025-07-15T14:30:57.457Z"}
{"level":"info","message":"⏳ PROGRESS: 🔍 Checking user existence...","timestamp":"2025-07-15T14:30:57.457Z"}
{"level":"info","message":"⏳ PROGRESS: Request attempt 1/3","method":"GET","timestamp":"2025-07-15T14:30:57.457Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/me"}
{"error":"Request failed with status code 404","level":"error","message":"❌ FAILURE: Request attempt 1 failed","status":404,"timestamp":"2025-07-15T14:31:01.765Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/me"}
{"level":"info","message":"⏳ PROGRESS: User not found - will create new account","timestamp":"2025-07-15T14:31:01.766Z"}
{"level":"info","message":"⏳ PROGRESS: 👤 Creating new account...","timestamp":"2025-07-15T14:31:01.766Z"}
{"level":"info","message":"⏳ PROGRESS: Request attempt 1/3","method":"POST","timestamp":"2025-07-15T14:31:01.766Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/create"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"❌ FAILURE: Request attempt 1 failed","timestamp":"2025-07-15T14:31:31.777Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/create"}
{"level":"info","message":"⏳ PROGRESS: Waiting 2376.4223073546477ms before retry...","timestamp":"2025-07-15T14:31:31.777Z"}
{"level":"info","message":"⏳ PROGRESS: Request attempt 2/3","method":"POST","timestamp":"2025-07-15T14:31:34.168Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/create"}
{"error":"Request failed with status code 400","level":"error","message":"❌ FAILURE: Request attempt 2 failed","status":400,"timestamp":"2025-07-15T14:31:38.045Z","url":"https://graph.cleanify.vet/api/v1/cleanify-user/create"}
{"error":"Request failed with status code 400","level":"error","message":"❌ FAILURE: Account creation failed","timestamp":"2025-07-15T14:31:38.045Z"}
{"error":"Request failed with status code 400","level":"error","message":"❌ FAILURE: Setup failed","timestamp":"2025-07-15T14:31:38.045Z"}
{"duration":"40.72s","error":"Request failed with status code 400","image":"image.png","level":"error","message":"❌ FAILURE: Image processing failed","timestamp":"2025-07-15T14:31:38.046Z"}
{"level":"warn","message":"🔒 SECURITY: Data encrypted successfully","timestamp":"2025-07-15T14:31:38.048Z"}
{"level":"warn","message":"🔒 SECURITY: Data encrypted successfully","timestamp":"2025-07-15T14:31:38.049Z"}
{"backupPath":"./data/accounts_backup_1752589898051.json","level":"info","message":"✅ SUCCESS: Account backup created","timestamp":"2025-07-15T14:31:38.053Z"}
{"count":2,"file":"./data/accounts.json","level":"info","message":"✅ SUCCESS: Accounts saved successfully","timestamp":"2025-07-15T14:31:38.054Z"}
{"address":"******************************************","failureCount":1,"id":"DTTT0dWzku7TbhUS","level":"info","message":"✅ SUCCESS: Wallet saved successfully","success":false,"successCount":0,"timestamp":"2025-07-15T14:31:38.056Z"}
{"level":"info","message":"⏳ PROGRESS: ========================================","timestamp":"2025-07-15T14:31:38.056Z"}
{"level":"info","message":"✅ SUCCESS: 🎯 EXECUTION COMPLETED","timestamp":"2025-07-15T14:31:38.057Z"}
{"level":"info","message":"⏳ PROGRESS: ========================================","timestamp":"2025-07-15T14:31:38.057Z"}
{"level":"info","message":"⏳ PROGRESS: 📊 Results Summary:","timestamp":"2025-07-15T14:31:38.057Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Images: 1","timestamp":"2025-07-15T14:31:38.058Z"}
{"level":"info","message":"⏳ PROGRESS:    • Successful: 0","timestamp":"2025-07-15T14:31:38.058Z"}
{"level":"info","message":"⏳ PROGRESS:    • Failed: 1","timestamp":"2025-07-15T14:31:38.059Z"}
{"level":"info","message":"⏳ PROGRESS:    • Success Rate: 0.0%","timestamp":"2025-07-15T14:31:38.060Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Rewards: 0 VET","timestamp":"2025-07-15T14:31:38.060Z"}
{"level":"info","message":"⏳ PROGRESS: 📈 Wallet Statistics:","timestamp":"2025-07-15T14:31:38.061Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Wallets: 2","timestamp":"2025-07-15T14:31:38.062Z"}
{"level":"info","message":"⏳ PROGRESS:    • Used Wallets: 2","timestamp":"2025-07-15T14:31:38.062Z"}
{"level":"info","message":"⏳ PROGRESS:    • Overall Success Rate: 0.00%","timestamp":"2025-07-15T14:31:38.063Z"}
{"level":"info","message":"⏳ PROGRESS: 🎯 Referral Statistics:","timestamp":"2025-07-15T14:31:38.063Z"}
{"level":"info","message":"⏳ PROGRESS:    • Referral Code: LD4BK","timestamp":"2025-07-15T14:31:38.064Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Referrals: 0","timestamp":"2025-07-15T14:31:38.065Z"}
{"level":"info","message":"⏳ PROGRESS:    • Successful: 0","timestamp":"2025-07-15T14:31:38.065Z"}
{"level":"info","message":"⏳ PROGRESS:    • Referral Success Rate: 0%","timestamp":"2025-07-15T14:31:38.065Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Referral Rewards: 0 VET","timestamp":"2025-07-15T14:31:38.065Z"}
{"level":"error","message":"❌ FAILURE: ❌ Failed Images:","timestamp":"2025-07-15T14:31:38.066Z"}
{"level":"error","message":"❌ FAILURE:    • image.png","timestamp":"2025-07-15T14:31:38.067Z"}
{"level":"info","message":"⏳ PROGRESS: ========================================","timestamp":"2025-07-15T14:31:38.067Z"}
{"file":"./data/bot_stats.json","level":"info","message":"✅ SUCCESS: Statistics saved","timestamp":"2025-07-15T14:31:38.071Z"}
{"level":"info","message":"⏳ PROGRESS: 🎯 Initializing Referral Manager...","timestamp":"2025-07-15T14:32:24.403Z"}
{"level":"info","message":"✅ SUCCESS: Referral manager initialized successfully","timestamp":"2025-07-15T14:32:24.408Z"}
{"level":"info","message":"✅ SUCCESS: 📊 REFERRAL STATISTICS","timestamp":"2025-07-15T14:32:24.408Z"}
{"level":"info","message":"⏳ PROGRESS: =======================","timestamp":"2025-07-15T14:32:24.409Z"}
{"level":"info","message":"⏳ PROGRESS: 🎯 Referral Code: LD4BK","timestamp":"2025-07-15T14:32:24.409Z"}
{"level":"info","message":"⏳ PROGRESS: 🔗 Referral Link: https://app.cleanify.vet/?referralCode=LD4BK","timestamp":"2025-07-15T14:32:24.409Z"}
{"level":"info","message":"⏳ PROGRESS: 📈 Total Referrals: 0","timestamp":"2025-07-15T14:32:24.409Z"}
{"level":"info","message":"⏳ PROGRESS: ✅ Successful: 0","timestamp":"2025-07-15T14:32:24.410Z"}
{"level":"info","message":"⏳ PROGRESS: ❌ Failed: 0","timestamp":"2025-07-15T14:32:24.410Z"}
{"level":"info","message":"⏳ PROGRESS: 📊 Success Rate: 0%","timestamp":"2025-07-15T14:32:24.410Z"}
{"level":"info","message":"⏳ PROGRESS: 💰 Total Rewards: 0 VET","timestamp":"2025-07-15T14:32:24.410Z"}
{"level":"info","message":"⏳ PROGRESS: 🕒 Last Updated: Never","timestamp":"2025-07-15T14:32:24.410Z"}
{"level":"info","message":"⏳ PROGRESS: \n👛 Wallet Statistics:","timestamp":"2025-07-15T14:32:24.411Z"}
{"level":"info","message":"⏳ PROGRESS:    • Total Wallets: 0","timestamp":"2025-07-15T14:32:24.411Z"}
{"level":"info","message":"⏳ PROGRESS:    • Used Wallets: 0","timestamp":"2025-07-15T14:32:24.411Z"}
{"level":"info","message":"⏳ PROGRESS:    • Unused Wallets: 0","timestamp":"2025-07-15T14:32:24.411Z"}
{"level":"info","message":"✅ SUCCESS: Loaded 2 existing accounts","timestamp":"2025-07-15T14:32:24.412Z"}
{"level":"info","message":"⏳ PROGRESS: 🎯 Initializing Referral Manager...","timestamp":"2025-07-15T14:32:40.229Z"}
{"level":"info","message":"✅ SUCCESS: Referral manager initialized successfully","timestamp":"2025-07-15T14:32:40.233Z"}
{"level":"info","message":"⏳ PROGRESS: 🔍 Checking referral rewards for all wallets...","timestamp":"2025-07-15T14:32:40.234Z"}
{"level":"error","message":"❌ FAILURE: No wallets found","timestamp":"2025-07-15T14:32:40.234Z"}
{"level":"info","message":"✅ SUCCESS: Loaded 2 existing accounts","timestamp":"2025-07-15T14:32:40.235Z"}
{"level":"info","message":"⏳ PROGRESS: 🎯 Initializing Referral Manager...","timestamp":"2025-07-15T14:32:51.269Z"}
{"level":"info","message":"✅ SUCCESS: Referral manager initialized successfully","timestamp":"2025-07-15T14:32:51.273Z"}
{"level":"info","message":"⏳ PROGRESS: 💰 Claiming referral rewards for all eligible wallets...","timestamp":"2025-07-15T14:32:51.273Z"}
{"level":"error","message":"❌ FAILURE: No wallets found","timestamp":"2025-07-15T14:32:51.274Z"}
{"level":"info","message":"✅ SUCCESS: Loaded 2 existing accounts","timestamp":"2025-07-15T14:32:51.274Z"}
{"level":"info","message":"⏳ PROGRESS: 🎯 Initializing Referral Manager...","timestamp":"2025-07-15T14:32:54.208Z"}
{"level":"info","message":"✅ SUCCESS: Referral manager initialized successfully","timestamp":"2025-07-15T14:32:54.213Z"}
{"level":"info","message":"⏳ PROGRESS: 💰 Claiming referral rewards for all eligible wallets...","timestamp":"2025-07-15T14:32:54.213Z"}
{"level":"error","message":"❌ FAILURE: No wallets found","timestamp":"2025-07-15T14:32:54.214Z"}
{"level":"info","message":"✅ SUCCESS: Loaded 2 existing accounts","timestamp":"2025-07-15T14:32:54.215Z"}
