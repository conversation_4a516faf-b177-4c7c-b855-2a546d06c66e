{"name": "cleanify-auto-upload", "version": "2.0.0", "description": "Advanced auto upload bot for Cleanify with enhanced security and reliability", "main": "upload.js", "type": "module", "scripts": {"start": "node upload.js", "upload": "node upload.js", "config": "node config.js", "test": "node test.js"}, "dependencies": {"@vechain/sdk-core": "^2.0.0", "@vechain/sdk-network": "^2.0.0", "ethers": "^6.8.1", "form-data": "^4.0.0", "keccak": "^3.0.4", "node-fetch": "^3.3.2", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "fs-extra": "^11.1.1", "sharp": "^0.32.6", "axios": "^1.6.0", "user-agents": "^1.1.0", "proxy-agent": "^6.3.1"}, "keywords": ["cleanify", "ipfs", "upload", "automation", "vechain", "bot"], "author": "Advanced Bot Developer", "license": "MIT"}