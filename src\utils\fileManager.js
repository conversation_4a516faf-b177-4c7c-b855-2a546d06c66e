import fs from 'fs-extra';
import path from 'path';
import sharp from 'sharp';
import logger from './logger.js';

class FileManager {
    constructor(config) {
        this.config = config;
        this.imageDir = config.files.imageDirectory || './images';
        this.dataDir = './data';
        this.supportedFormats = config.files.supportedFormats || ['png', 'jpg', 'jpeg', 'gif', 'webp'];
        this.maxFileSize = config.files.maxFileSize || 10485760; // 10MB
        this.imageQuality = config.files.imageQuality || 80;
        
        this.initializeDirectories();
    }

    /**
     * Initialize required directories
     */
    async initializeDirectories() {
        try {
            await fs.ensureDir(this.imageDir);
            await fs.ensureDir(this.dataDir);
            await fs.ensureDir('./logs');
            logger.success('Directories initialized');
        } catch (error) {
            logger.failure('Failed to initialize directories', { error: error.message });
            throw error;
        }
    }

    /**
     * Validate image file
     * @param {string} filePath - Path to image file
     * @returns {Promise<Object>} - Validation result
     */
    async validateImage(filePath) {
        try {
            // Check if file exists
            if (!await fs.pathExists(filePath)) {
                throw new Error(`File not found: ${filePath}`);
            }

            // Get file stats
            const stats = await fs.stat(filePath);
            
            // Check file size
            if (stats.size > this.maxFileSize) {
                throw new Error(`File too large: ${stats.size} bytes (max: ${this.maxFileSize})`);
            }

            // Check file extension
            const ext = path.extname(filePath).toLowerCase().slice(1);
            if (!this.supportedFormats.includes(ext)) {
                throw new Error(`Unsupported format: ${ext} (supported: ${this.supportedFormats.join(', ')})`);
            }

            // Validate image using sharp
            const metadata = await sharp(filePath).metadata();
            
            const validation = {
                valid: true,
                path: filePath,
                size: stats.size,
                format: metadata.format,
                width: metadata.width,
                height: metadata.height,
                channels: metadata.channels
            };

            logger.success('Image validation passed', validation);
            return validation;

        } catch (error) {
            logger.failure('Image validation failed', { 
                filePath, 
                error: error.message 
            });
            throw error;
        }
    }

    /**
     * Optimize image for upload
     * @param {string} inputPath - Input image path
     * @param {string} outputPath - Output image path (optional)
     * @returns {Promise<string>} - Optimized image path
     */
    async optimizeImage(inputPath, outputPath = null) {
        try {
            if (!outputPath) {
                const parsed = path.parse(inputPath);
                outputPath = path.join(parsed.dir, `${parsed.name}_optimized${parsed.ext}`);
            }

            const metadata = await sharp(inputPath).metadata();
            
            let pipeline = sharp(inputPath);

            // Resize if too large
            if (metadata.width > 1920 || metadata.height > 1920) {
                pipeline = pipeline.resize(1920, 1920, {
                    fit: 'inside',
                    withoutEnlargement: true
                });
            }

            // Optimize based on format
            switch (metadata.format) {
                case 'jpeg':
                case 'jpg':
                    pipeline = pipeline.jpeg({ quality: this.imageQuality });
                    break;
                case 'png':
                    pipeline = pipeline.png({ quality: this.imageQuality });
                    break;
                case 'webp':
                    pipeline = pipeline.webp({ quality: this.imageQuality });
                    break;
                default:
                    // Convert to JPEG for other formats
                    pipeline = pipeline.jpeg({ quality: this.imageQuality });
                    outputPath = outputPath.replace(path.extname(outputPath), '.jpg');
            }

            await pipeline.toFile(outputPath);

            const originalSize = (await fs.stat(inputPath)).size;
            const optimizedSize = (await fs.stat(outputPath)).size;
            const savings = ((originalSize - optimizedSize) / originalSize * 100).toFixed(1);

            logger.success('Image optimized', {
                originalSize,
                optimizedSize,
                savings: `${savings}%`,
                outputPath
            });

            return outputPath;

        } catch (error) {
            logger.failure('Image optimization failed', { 
                inputPath, 
                error: error.message 
            });
            throw error;
        }
    }

    /**
     * Get all images from directory
     * @param {string} directory - Directory to scan
     * @returns {Promise<Array>} - Array of image paths
     */
    async getImages(directory = this.imageDir) {
        try {
            const files = await fs.readdir(directory);
            const images = [];

            for (const file of files) {
                const filePath = path.join(directory, file);
                const ext = path.extname(file).toLowerCase().slice(1);
                
                if (this.supportedFormats.includes(ext)) {
                    try {
                        await this.validateImage(filePath);
                        images.push(filePath);
                    } catch (error) {
                        logger.failure(`Skipping invalid image: ${file}`, { error: error.message });
                    }
                }
            }

            logger.success(`Found ${images.length} valid images`, { directory });
            return images;

        } catch (error) {
            logger.failure('Failed to get images', { directory, error: error.message });
            throw error;
        }
    }

    /**
     * Create backup of file
     * @param {string} filePath - File to backup
     * @returns {Promise<string>} - Backup file path
     */
    async createBackup(filePath) {
        try {
            const parsed = path.parse(filePath);
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupPath = path.join(parsed.dir, 'backups', `${parsed.name}_${timestamp}${parsed.ext}`);
            
            await fs.ensureDir(path.dirname(backupPath));
            await fs.copy(filePath, backupPath);
            
            logger.success('Backup created', { original: filePath, backup: backupPath });
            return backupPath;

        } catch (error) {
            logger.failure('Failed to create backup', { filePath, error: error.message });
            throw error;
        }
    }

    /**
     * Clean up temporary files
     * @param {Array} filePaths - Array of file paths to clean up
     */
    async cleanup(filePaths) {
        for (const filePath of filePaths) {
            try {
                if (await fs.pathExists(filePath)) {
                    await fs.remove(filePath);
                    logger.progress(`Cleaned up: ${filePath}`);
                }
            } catch (error) {
                logger.failure(`Failed to cleanup: ${filePath}`, { error: error.message });
            }
        }
    }
}

export default FileManager;
