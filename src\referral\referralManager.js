import fs from 'fs-extra';
import path from 'path';
import logger from '../utils/logger.js';

class ReferralManager {
    constructor(config, networkManager) {
        this.config = config;
        this.networkManager = networkManager;
        this.referralCode = config.referral.referralCode || process.env.REFERRAL_CODE;
        this.enabled = config.referral.enabled && this.referralCode;
        this.referralDataFile = './data/referral_data.json';
        this.referralStats = {
            totalReferrals: 0,
            successfulReferrals: 0,
            failedReferrals: 0,
            totalReferralRewards: 0,
            lastUpdated: null
        };
        
        this.loadReferralData();
    }

    /**
     * Load referral data from file
     */
    async loadReferralData() {
        try {
            if (await fs.pathExists(this.referralDataFile)) {
                const data = await fs.readJson(this.referralDataFile);
                this.referralStats = { ...this.referralStats, ...data.stats };
                logger.success('Referral data loaded', { 
                    totalReferrals: this.referralStats.totalReferrals,
                    totalRewards: this.referralStats.totalReferralRewards
                });
            }
        } catch (error) {
            logger.failure('Failed to load referral data', { error: error.message });
        }
    }

    /**
     * Save referral data to file
     */
    async saveReferralData() {
        try {
            await fs.ensureDir(path.dirname(this.referralDataFile));
            
            const data = {
                version: '2.0.0',
                referralCode: this.referralCode,
                lastUpdated: new Date().toISOString(),
                stats: this.referralStats
            };

            await fs.writeJson(this.referralDataFile, data, { spaces: 2 });
            logger.success('Referral data saved');

        } catch (error) {
            logger.failure('Failed to save referral data', { error: error.message });
        }
    }

    /**
     * Apply referral code during account creation
     * @param {Object} wallet - Wallet object
     * @param {string} certificate - Authentication certificate
     * @returns {Promise<Object>} - Referral application result
     */
    async applyReferral(wallet, certificate) {
        if (!this.enabled) {
            logger.progress('Referral system disabled');
            return { success: false, reason: 'disabled' };
        }

        try {
            logger.progress(`🎯 Applying referral code: ${this.referralCode}`);

            const payload = {
                referralCode: this.referralCode,
                referredUser: wallet.address.toLowerCase()
            };

            const response = await this.networkManager.makeRequest({
                method: 'POST',
                url: this.config.api.referralUrl,
                headers: {
                    'Content-Type': 'application/json',
                    'x-wallet-address': wallet.address.toLowerCase(),
                    'Authorization': certificate,
                    'Origin': 'https://app.cleanify.vet',
                    'Referer': 'https://app.cleanify.vet/'
                },
                data: payload
            });

            this.referralStats.totalReferrals++;
            this.referralStats.successfulReferrals++;
            this.referralStats.lastUpdated = new Date().toISOString();

            await this.saveReferralData();

            logger.success('Referral applied successfully', {
                referralCode: this.referralCode,
                referredUser: wallet.address,
                response: response.message || 'Success'
            });

            return { 
                success: true, 
                referralCode: this.referralCode,
                response 
            };

        } catch (error) {
            this.referralStats.totalReferrals++;
            this.referralStats.failedReferrals++;
            this.referralStats.lastUpdated = new Date().toISOString();

            await this.saveReferralData();

            // Don't throw error if referral fails - it's not critical
            if (error.response?.status === 409) {
                logger.progress('Referral already applied or invalid');
                return { success: false, reason: 'already_applied' };
            } else if (error.response?.status === 404) {
                logger.progress('Referral code not found or expired');
                return { success: false, reason: 'invalid_code' };
            } else {
                logger.failure('Referral application failed', { 
                    error: error.message,
                    status: error.response?.status
                });
                return { success: false, reason: 'network_error', error: error.message };
            }
        }
    }

    /**
     * Check referral rewards
     * @param {Object} wallet - Wallet object
     * @param {string} certificate - Authentication certificate
     * @returns {Promise<Object>} - Referral rewards info
     */
    async checkReferralRewards(wallet, certificate) {
        if (!this.enabled || !this.config.referral.trackReferralRewards) {
            return { success: false, reason: 'disabled' };
        }

        try {
            logger.progress('🎁 Checking referral rewards...');

            const response = await this.networkManager.makeRequest({
                method: 'GET',
                url: `${this.config.api.referralUrl}/rewards`,
                headers: {
                    'x-wallet-address': wallet.address.toLowerCase(),
                    'Authorization': certificate,
                    'Origin': 'https://app.cleanify.vet',
                    'Referer': 'https://app.cleanify.vet/'
                }
            });

            const rewards = response.data || {};
            const totalRewards = rewards.totalRewards || 0;
            const pendingRewards = rewards.pendingRewards || 0;
            const claimableRewards = rewards.claimableRewards || 0;

            logger.success('Referral rewards checked', {
                totalRewards,
                pendingRewards,
                claimableRewards
            });

            return {
                success: true,
                totalRewards,
                pendingRewards,
                claimableRewards,
                details: rewards
            };

        } catch (error) {
            logger.failure('Failed to check referral rewards', { error: error.message });
            return { success: false, error: error.message };
        }
    }

    /**
     * Claim referral rewards
     * @param {Object} wallet - Wallet object
     * @param {string} certificate - Authentication certificate
     * @returns {Promise<Object>} - Claim result
     */
    async claimReferralRewards(wallet, certificate) {
        if (!this.enabled || !this.config.referral.trackReferralRewards) {
            return { success: false, reason: 'disabled' };
        }

        try {
            // First check if there are claimable rewards
            const rewardsCheck = await this.checkReferralRewards(wallet, certificate);
            
            if (!rewardsCheck.success || rewardsCheck.claimableRewards <= 0) {
                logger.progress('No claimable referral rewards available');
                return { success: false, reason: 'no_rewards' };
            }

            if (rewardsCheck.claimableRewards < this.config.referral.referralRewardThreshold) {
                logger.progress(`Referral rewards below threshold (${this.config.referral.referralRewardThreshold})`);
                return { success: false, reason: 'below_threshold' };
            }

            logger.progress('💰 Claiming referral rewards...');

            const response = await this.networkManager.makeRequest({
                method: 'POST',
                url: `${this.config.api.referralUrl}/claim`,
                headers: {
                    'Content-Type': 'application/json',
                    'x-wallet-address': wallet.address.toLowerCase(),
                    'Authorization': certificate,
                    'Origin': 'https://app.cleanify.vet',
                    'Referer': 'https://app.cleanify.vet/'
                }
            });

            const claimedAmount = response.data?.claimedAmount || rewardsCheck.claimableRewards;
            this.referralStats.totalReferralRewards += claimedAmount;
            this.referralStats.lastUpdated = new Date().toISOString();

            await this.saveReferralData();

            logger.success('Referral rewards claimed successfully', {
                claimedAmount,
                totalReferralRewards: this.referralStats.totalReferralRewards
            });

            return {
                success: true,
                claimedAmount,
                totalReferralRewards: this.referralStats.totalReferralRewards,
                response
            };

        } catch (error) {
            logger.failure('Failed to claim referral rewards', { error: error.message });
            return { success: false, error: error.message };
        }
    }

    /**
     * Get referral statistics
     * @returns {Object} - Referral statistics
     */
    getStats() {
        const successRate = this.referralStats.totalReferrals > 0 
            ? ((this.referralStats.successfulReferrals / this.referralStats.totalReferrals) * 100).toFixed(2)
            : 0;

        return {
            ...this.referralStats,
            referralCode: this.referralCode,
            enabled: this.enabled,
            successRate: `${successRate}%`
        };
    }

    /**
     * Generate referral link
     * @returns {string} - Referral link
     */
    getReferralLink() {
        if (!this.referralCode) {
            return null;
        }
        return `https://app.cleanify.vet/?referralCode=${this.referralCode}`;
    }

    /**
     * Display referral information
     */
    displayInfo() {
        if (!this.enabled) {
            logger.progress('🎯 Referral System: DISABLED');
            return;
        }

        logger.progress('🎯 Referral System Information:');
        logger.progress(`   • Referral Code: ${this.referralCode}`);
        logger.progress(`   • Referral Link: ${this.getReferralLink()}`);
        logger.progress(`   • Total Referrals: ${this.referralStats.totalReferrals}`);
        logger.progress(`   • Successful: ${this.referralStats.successfulReferrals}`);
        logger.progress(`   • Failed: ${this.referralStats.failedReferrals}`);
        logger.progress(`   • Total Rewards: ${this.referralStats.totalReferralRewards} VET`);
        logger.progress(`   • Success Rate: ${this.getStats().successRate}`);
    }
}

export default ReferralManager;
