import dotenv from 'dotenv';
import fs from 'fs-extra';
import path from 'path';
import FormData from 'form-data';
import { Address, Clause, ABIContract } from '@vechain/sdk-core';
import { Wallet } from 'ethers';
import { Buffer } from 'node:buffer';
import { ThorClient, ProviderInternalBaseWallet, VeChainProvider } from '@vechain/sdk-network';

// Import our enhanced utilities
import logger from './src/utils/logger.js';
import CryptoManager from './src/utils/crypto.js';
import NetworkManager from './src/utils/network.js';
import FileManager from './src/utils/fileManager.js';
import WalletManager from './src/wallet/walletManager.js';

// Load environment variables
dotenv.config();

class CleanifyBot {
    constructor() {
        this.config = null;
        this.cryptoManager = null;
        this.networkManager = null;
        this.fileManager = null;
        this.walletManager = null;
        this.currentWallet = null;
        this.currentCertificate = null;
        this.stats = {
            totalRuns: 0,
            successfulRuns: 0,
            failedRuns: 0,
            totalRewards: 0
        };
    }

    async initialize() {
        try {
            logger.progress('🚀 Initializing Cleanify Bot v2.0...');

            // Load configuration
            this.config = await fs.readJson('./config.json');
            
            // Initialize managers
            this.cryptoManager = new CryptoManager(process.env.ENCRYPTION_KEY);
            this.networkManager = new NetworkManager(this.config);
            this.fileManager = new FileManager(this.config);
            this.walletManager = new WalletManager(this.config, this.cryptoManager);

            logger.success('Bot initialized successfully');
            
            // Display configuration
            this.displayConfig();

        } catch (error) {
            logger.failure('Failed to initialize bot', { error: error.message });
            throw error;
        }
    }

    displayConfig() {
        logger.progress('📋 Current Configuration:');
        logger.progress(`   • Debug Mode: ${this.config.app.debug}`);
        logger.progress(`   • Max Retries: ${this.config.app.maxRetries}`);
        logger.progress(`   • Encrypt Keys: ${this.config.security.encryptPrivateKeys}`);
        logger.progress(`   • Random UA: ${this.config.security.randomizeUserAgent}`);
        logger.progress(`   • Use Proxy: ${this.config.security.useProxy}`);
        logger.progress(`   • Batch Mode: ${this.config.automation.batchMode}`);
        logger.progress(`   • Reuse Wallet: ${this.config.wallet.reuseExisting}`);
    }

    async generateCertificate(wallet) {
        try {
            logger.progress('📝 Generating authentication certificate...');

            const value = {
                user: wallet.address.toLowerCase(),
                isSocialLogin: false,
                timestamp: new Date().toISOString()
            };

            const domain = {
                name: 'Cleanify',
                version: '1',
                chainId: 1,
                verifyingContract: this.config.contract.verifyingContract
            };

            const types = {
                Authentication: [
                    { name: 'user', type: 'address' },
                    { name: 'isSocialLogin', type: 'bool' },
                    { name: 'timestamp', type: 'string' }
                ]
            };

            const signer = new Wallet(wallet.privateKey);
            const signature = await signer.signTypedData(domain, types, value);

            const certificate = { address: wallet.address, value, signature };
            const certB64 = Buffer.from(JSON.stringify(certificate)).toString('base64');

            logger.success('Certificate generated successfully');
            return `certificate ${certB64}`;

        } catch (error) {
            logger.failure('Failed to generate certificate', { error: error.message });
            throw error;
        }
    }

    async checkUserExistence(wallet, certificate) {
        try {
            logger.progress('🔍 Checking user existence...');

            const response = await this.networkManager.makeRequest({
                method: 'GET',
                url: this.config.api.checkUserUrl,
                headers: {
                    'x-wallet-address': wallet.address,
                    'Authorization': certificate
                }
            });

            logger.success('User already exists');
            return { exists: true, user: response };

        } catch (error) {
            if (error.response?.status === 404) {
                logger.progress('User not found - will create new account');
                return { exists: false, message: 'User not found' };
            }
            
            logger.failure('User check failed', { error: error.message });
            throw error;
        }
    }

    async createAccount(wallet, certificate) {
        try {
            logger.progress('👤 Creating new account...');

            const payload = {
                name: "",
                surname: ""
            };

            const response = await this.networkManager.makeRequest({
                method: 'POST',
                url: this.config.api.createUserUrl,
                headers: {
                    'Content-Type': 'application/json',
                    'x-wallet-address': wallet.address,
                    'Authorization': certificate
                },
                data: payload
            });

            logger.success('Account created successfully');
            return { success: true, result: response, created: true };

        } catch (error) {
            if (error.response?.status === 409) {
                logger.progress('Account already exists');
                return { success: true, message: 'Account already exists', created: false };
            }
            
            logger.failure('Account creation failed', { error: error.message });
            throw error;
        }
    }

    async setupWalletAndAccount() {
        try {
            logger.progress('🔧 Setting up wallet and account...');

            // Get wallet (existing or new)
            this.currentWallet = await this.walletManager.getWallet();
            
            // Generate certificate
            this.currentCertificate = await this.generateCertificate(this.currentWallet);

            // Check if user exists
            const userCheckResult = await this.checkUserExistence(this.currentWallet, this.currentCertificate);

            if (!userCheckResult.exists) {
                await this.createAccount(this.currentWallet, this.currentCertificate);
            }

            logger.success('Wallet and account setup completed', {
                address: this.currentWallet.address,
                userExists: userCheckResult.exists
            });

            return { wallet: this.currentWallet, certificate: this.currentCertificate };

        } catch (error) {
            logger.failure('Setup failed', { error: error.message });
            throw error;
        }
    }

    async uploadImageToIPFS(imagePath) {
        try {
            logger.progress('📁 Preparing image upload...');

            // Validate image
            const validation = await this.fileManager.validateImage(imagePath);
            
            // Optimize image if needed
            let optimizedPath = imagePath;
            if (validation.size > 1048576) { // 1MB
                optimizedPath = await this.fileManager.optimizeImage(imagePath);
            }

            // Create form data
            const form = new FormData();
            const imageStream = fs.createReadStream(optimizedPath);
            const filename = path.basename(optimizedPath);

            form.append('file', imageStream, {
                filename: filename,
                contentType: `image/${validation.format}`
            });

            logger.progress('⬆️ Uploading to IPFS...');

            // Upload using network manager
            const response = await this.networkManager.uploadFile(
                this.config.api.ipfsUrl,
                form,
                {
                    'accept': '*/*',
                    'origin': 'https://app.cleanify.vet',
                    'referer': 'https://app.cleanify.vet/'
                }
            );

            // Cleanup optimized file if different from original
            if (optimizedPath !== imagePath) {
                await this.fileManager.cleanup([optimizedPath]);
            }

            logger.success('IPFS upload successful', {
                hash: response.IpfsHash,
                size: response.PinSize
            });

            return response;

        } catch (error) {
            logger.failure('IPFS upload failed', { error: error.message });
            throw error;
        }
    }

    async submitDailyAction(ipfsHash) {
        try {
            logger.progress('📝 Submitting daily action...');

            const ipfsUrl = `https://api.gateway-proxy.vechain.org/ipfs/${ipfsHash}`;
            const timestamp = new Date().toISOString();

            const payload = {
                type: "butt-disposal",
                picture: {
                    url: ipfsUrl,
                    timestamp: timestamp
                }
            };

            const response = await this.networkManager.makeRequest({
                method: 'POST',
                url: this.config.api.dailyActionUrl,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': this.currentCertificate,
                    'x-wallet-address': this.currentWallet.address.toLowerCase(),
                    'Origin': 'https://app.cleanify.vet',
                    'Referer': 'https://app.cleanify.vet/'
                },
                data: payload
            });

            logger.success('Daily action submitted successfully', {
                actionId: response.data?.id
            });

            return response;

        } catch (error) {
            logger.failure('Daily action submission failed', { error: error.message });
            throw error;
        }
    }

    async sendContractCall(claimData) {
        try {
            logger.progress('⛓️ Building blockchain transaction...');

            const thor = ThorClient.fromUrl(this.config.api.vchainNodeUrl);
            const contract = ABIContract.ofAbi([{
                inputs: [{
                    components: [
                        { internalType: "enum RewardClaimer.RewardType", name: "rewardType", type: "uint8" },
                        { internalType: "uint256", name: "amount", type: "uint256" },
                        { internalType: "string[]", name: "proofTypes", type: "string[]" },
                        { internalType: "string[]", name: "proofValues", type: "string[]" },
                        { internalType: "string[]", name: "impactCodes", type: "string[]" },
                        { internalType: "uint256[]", name: "impactValues", type: "uint256[]" },
                        { internalType: "string", name: "description", type: "string" },
                        { internalType: "string", name: "claimId", type: "string" },
                        { internalType: "bytes", name: "signature", type: "bytes" }
                    ],
                    internalType: "struct RewardClaimer.ClaimParams",
                    name: "params",
                    type: "tuple"
                }],
                name: "claimReward",
                outputs: [],
                stateMutability: "nonpayable",
                type: "function"
            }]);

            const clause = Clause.callFunction(
                Address.of(this.config.contract.rewardClaimerAddress),
                contract.getFunction('claimReward'),
                [{
                    ...claimData.payload,
                    signature: claimData.signature,
                    claimId: claimData.payload.claimId
                }]
            );

            logger.progress('⛽ Estimating gas...');
            const gasResult = await thor.transactions.estimateGas([clause], this.currentWallet.address);

            logger.progress('📝 Building transaction body...');
            const txBody = await thor.transactions.buildTransactionBody(
                [clause],
                gasResult.totalGas,
                {
                    isDelegated: true,
                    origin: this.currentWallet.address
                }
            );

            logger.progress('🔑 Preparing signer...');
            const wallet = new ProviderInternalBaseWallet([
                { privateKey: this.currentWallet.privateKeyBytes, address: this.currentWallet.address },
            ], {
                gasPayer: {
                    gasPayerServiceUrl: this.config.api.feeDelegationUrl
                },
            });

            const provider = new VeChainProvider(thor, wallet, true);
            const signer = await provider.getSigner(this.currentWallet.address);

            logger.progress('✍️ Signing transaction...');
            const rawSignedTx = await signer.signTransaction(txBody, this.currentWallet.privateKey);

            logger.progress('📤 Broadcasting transaction...');
            const broadcastRes = await this.networkManager.makeRequest({
                method: 'POST',
                url: `${this.config.api.vchainNodeUrl}/transactions`,
                headers: { 'Content-Type': 'application/json' },
                data: { raw: rawSignedTx }
            });

            logger.success('Transaction sent', { txId: broadcastRes.id });

            logger.progress('⏳ Waiting for receipt...');
            const txReceipt = await thor.transactions.waitForTransaction(broadcastRes.id);

            logger.success('Transaction confirmed', {
                txId: broadcastRes.id,
                gasUsed: txReceipt.gasUsed
            });

            return txReceipt;

        } catch (error) {
            logger.failure('Contract call failed', { error: error.message });
            throw error;
        }
    }

    async claimRewardAPI(actionId) {
        try {
            logger.progress('🎯 Finalizing claim via API...');

            const claimUrl = `${this.config.api.dailyActionUrl}/${actionId}/claim`;

            const response = await this.networkManager.makeRequest({
                method: 'POST',
                url: claimUrl,
                headers: {
                    'Content-Type': 'application/json',
                    'x-wallet-address': this.currentWallet.address.toLowerCase(),
                    'Authorization': this.currentCertificate,
                    'Origin': 'https://app.cleanify.vet',
                    'Referer': 'https://app.cleanify.vet/'
                }
            });

            const reward = response.data?.tokenReward || 0;
            this.stats.totalRewards += parseFloat(reward);

            logger.success('API claim successful', {
                reward: `${reward} VET`,
                totalRewards: `${this.stats.totalRewards} VET`
            });

            return { success: true, result: response };

        } catch (error) {
            logger.failure('API claim failed', { error: error.message });
            return { success: false, error: error.message };
        }
    }

    async processSingleImage(imagePath) {
        const startTime = Date.now();
        let success = false;

        try {
            logger.progress(`🖼️ Processing image: ${path.basename(imagePath)}`);

            // Setup wallet and account
            await this.setupWalletAndAccount();

            // Add random delay
            await this.networkManager.randomDelay();

            // Upload image to IPFS
            const ipfsResult = await this.uploadImageToIPFS(imagePath);
            if (!ipfsResult.IpfsHash) {
                throw new Error('No IPFS hash received from upload');
            }

            // Submit daily action
            const actionResult = await this.submitDailyAction(ipfsResult.IpfsHash);

            if (actionResult.success && actionResult.data?.signature && actionResult.data?.payload) {
                // Send contract call
                const contractResult = await this.sendContractCall(actionResult.data);

                if (contractResult.meta) {
                    // Claim reward via API
                    const apiClaimResult = await this.claimRewardAPI(actionResult.data.id);

                    success = apiClaimResult.success;

                    logger.success('🎉 Process completed successfully', {
                        wallet: this.currentWallet.address,
                        txId: contractResult.txId,
                        reward: apiClaimResult.result?.data?.tokenReward || 'Unknown',
                        duration: `${((Date.now() - startTime) / 1000).toFixed(2)}s`
                    });
                } else {
                    logger.failure('Contract call failed but daily action succeeded');
                }
            } else {
                logger.failure('Daily action completed but no reward data received');
            }

        } catch (error) {
            logger.failure('Image processing failed', {
                image: path.basename(imagePath),
                error: error.message,
                duration: `${((Date.now() - startTime) / 1000).toFixed(2)}s`
            });
        } finally {
            // Update wallet stats
            if (this.currentWallet) {
                await this.walletManager.saveWallet(this.currentWallet, success);
            }

            // Update bot stats
            this.stats.totalRuns++;
            if (success) {
                this.stats.successfulRuns++;
            } else {
                this.stats.failedRuns++;
            }
        }

        return success;
    }

    async processBatch(imagePaths) {
        logger.progress(`🔄 Starting batch processing: ${imagePaths.length} images`);

        const results = [];
        const batchSize = this.config.automation.batchSize || 5;

        for (let i = 0; i < imagePaths.length; i += batchSize) {
            const batch = imagePaths.slice(i, i + batchSize);
            logger.progress(`Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(imagePaths.length/batchSize)}`);

            const batchPromises = batch.map(async (imagePath) => {
                const result = await this.processSingleImage(imagePath);

                // Add delay between actions
                if (this.config.automation.delayBetweenActions > 0) {
                    await this.networkManager.sleep(this.config.automation.delayBetweenActions);
                }

                return { imagePath, success: result };
            });

            const batchResults = await Promise.allSettled(batchPromises);
            results.push(...batchResults.map(r => r.value || { success: false }));
        }

        return results;
    }

    async run() {
        try {
            logger.success('🌟 CLEANIFY AUTO UPLOADER v2.0 🌟');
            logger.progress('========================================');

            // Get images to process
            let imagePaths;

            // Check if specific image file exists (backward compatibility)
            if (await fs.pathExists('./image.png')) {
                imagePaths = ['./image.png'];
                logger.progress('Using legacy image.png file');
            } else {
                // Get all images from images directory
                imagePaths = await this.fileManager.getImages();
                if (imagePaths.length === 0) {
                    throw new Error('No valid images found. Please add images to ./images/ directory or place image.png in root.');
                }
            }

            logger.progress(`Found ${imagePaths.length} image(s) to process`);

            // Process images
            let results;
            if (this.config.automation.batchMode && imagePaths.length > 1) {
                results = await this.processBatch(imagePaths);
            } else {
                results = [];
                for (const imagePath of imagePaths) {
                    const result = await this.processSingleImage(imagePath);
                    results.push({ imagePath, success: result });

                    // Add delay between images if not in batch mode
                    if (imagePaths.length > 1 && this.config.automation.delayBetweenActions > 0) {
                        await this.networkManager.sleep(this.config.automation.delayBetweenActions);
                    }
                }
            }

            // Display final results
            this.displayResults(results);

        } catch (error) {
            logger.failure('Bot execution failed', { error: error.message });
            process.exit(1);
        }
    }

    displayResults(results) {
        logger.progress('========================================');
        logger.success('🎯 EXECUTION COMPLETED');
        logger.progress('========================================');

        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;
        const successRate = results.length > 0 ? ((successful / results.length) * 100).toFixed(1) : 0;

        logger.progress(`📊 Results Summary:`);
        logger.progress(`   • Total Images: ${results.length}`);
        logger.progress(`   • Successful: ${successful}`);
        logger.progress(`   • Failed: ${failed}`);
        logger.progress(`   • Success Rate: ${successRate}%`);
        logger.progress(`   • Total Rewards: ${this.stats.totalRewards} VET`);

        // Display wallet stats
        const walletStats = this.walletManager.getStats();
        logger.progress(`📈 Wallet Statistics:`);
        logger.progress(`   • Total Wallets: ${walletStats.totalWallets}`);
        logger.progress(`   • Used Wallets: ${walletStats.usedWallets}`);
        logger.progress(`   • Overall Success Rate: ${walletStats.successRate}%`);

        // Display failed images
        const failedImages = results.filter(r => !r.success);
        if (failedImages.length > 0) {
            logger.failure('❌ Failed Images:');
            failedImages.forEach(result => {
                logger.failure(`   • ${path.basename(result.imagePath)}`);
            });
        }

        logger.progress('========================================');

        // Save final stats
        this.saveStats();
    }

    async saveStats() {
        try {
            const statsFile = './data/bot_stats.json';
            await fs.ensureDir('./data');

            const stats = {
                lastRun: new Date().toISOString(),
                totalRuns: this.stats.totalRuns,
                successfulRuns: this.stats.successfulRuns,
                failedRuns: this.stats.failedRuns,
                totalRewards: this.stats.totalRewards,
                walletStats: this.walletManager.getStats()
            };

            await fs.writeJson(statsFile, stats, { spaces: 2 });
            logger.success('Statistics saved', { file: statsFile });

        } catch (error) {
            logger.failure('Failed to save statistics', { error: error.message });
        }
    }
}

// Main execution
async function main() {
    const bot = new CleanifyBot();

    try {
        await bot.initialize();
        await bot.run();
    } catch (error) {
        logger.failure('Fatal error', { error: error.message });
        process.exit(1);
    }
}

// Handle process signals
process.on('SIGINT', () => {
    logger.progress('Received SIGINT, shutting down gracefully...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    logger.progress('Received SIGTERM, shutting down gracefully...');
    process.exit(0);
});

// Run the bot
main().catch(error => {
    logger.failure('Unhandled error', { error: error.message });
    process.exit(1);
});
