#!/usr/bin/env node

import dotenv from 'dotenv';
import fs from 'fs-extra';
import logger from './src/utils/logger.js';
import CryptoManager from './src/utils/crypto.js';
import NetworkManager from './src/utils/network.js';
import WalletManager from './src/wallet/walletManager.js';
import ReferralManager from './src/referral/referralManager.js';

// Load environment variables
dotenv.config();

class ReferralBot {
    constructor() {
        this.config = null;
        this.cryptoManager = null;
        this.networkManager = null;
        this.walletManager = null;
        this.referralManager = null;
    }

    async initialize() {
        try {
            logger.progress('🎯 Initializing Referral Manager...');

            // Load configuration
            this.config = await fs.readJson('./config.json');
            
            // Initialize managers
            this.cryptoManager = new CryptoManager(process.env.ENCRYPTION_KEY);
            this.networkManager = new NetworkManager(this.config);
            this.walletManager = new WalletManager(this.config, this.cryptoManager);
            this.referralManager = new ReferralManager(this.config, this.networkManager);

            logger.success('Referral manager initialized successfully');

        } catch (error) {
            logger.failure('Failed to initialize referral manager', { error: error.message });
            throw error;
        }
    }

    async showReferralInfo() {
        logger.success('🎯 CLEANIFY REFERRAL SYSTEM 🎯');
        logger.progress('=====================================');
        
        this.referralManager.displayInfo();
        
        logger.progress('=====================================');
        logger.progress('📋 Available Commands:');
        logger.progress('   • node referral.js info     - Show referral information');
        logger.progress('   • node referral.js stats    - Show detailed statistics');
        logger.progress('   • node referral.js check    - Check referral rewards');
        logger.progress('   • node referral.js claim    - Claim referral rewards');
        logger.progress('   • node referral.js apply    - Apply referral to existing wallets');
        logger.progress('=====================================');
    }

    async showStats() {
        logger.success('📊 REFERRAL STATISTICS');
        logger.progress('=======================');
        
        const stats = this.referralManager.getStats();
        
        if (!stats.enabled) {
            logger.failure('Referral system is disabled');
            return;
        }

        logger.progress(`🎯 Referral Code: ${stats.referralCode}`);
        logger.progress(`🔗 Referral Link: ${this.referralManager.getReferralLink()}`);
        logger.progress(`📈 Total Referrals: ${stats.totalReferrals}`);
        logger.progress(`✅ Successful: ${stats.successfulReferrals}`);
        logger.progress(`❌ Failed: ${stats.failedReferrals}`);
        logger.progress(`📊 Success Rate: ${stats.successRate}`);
        logger.progress(`💰 Total Rewards: ${stats.totalReferralRewards} VET`);
        logger.progress(`🕒 Last Updated: ${stats.lastUpdated || 'Never'}`);
        
        // Show wallet stats
        const walletStats = this.walletManager.getStats();
        logger.progress(`\n👛 Wallet Statistics:`);
        logger.progress(`   • Total Wallets: ${walletStats.totalWallets}`);
        logger.progress(`   • Used Wallets: ${walletStats.usedWallets}`);
        logger.progress(`   • Unused Wallets: ${walletStats.unusedWallets}`);
    }

    async checkRewards() {
        logger.progress('🔍 Checking referral rewards for all wallets...');
        
        const accounts = this.walletManager.accounts;
        if (accounts.length === 0) {
            logger.failure('No wallets found');
            return;
        }

        let totalClaimable = 0;
        let totalPending = 0;
        let checkedWallets = 0;

        for (const account of accounts) {
            try {
                // Get wallet
                const wallet = await this.walletManager.getWallet();
                if (wallet.id !== account.id) continue;

                // Generate certificate
                const certificate = await this.generateCertificate(wallet);

                // Check rewards
                const rewardsInfo = await this.referralManager.checkReferralRewards(wallet, certificate);
                
                if (rewardsInfo.success) {
                    totalClaimable += rewardsInfo.claimableRewards || 0;
                    totalPending += rewardsInfo.pendingRewards || 0;
                    checkedWallets++;

                    if (rewardsInfo.claimableRewards > 0) {
                        logger.success(`💰 ${wallet.address}: ${rewardsInfo.claimableRewards} VET claimable`);
                    }
                }

            } catch (error) {
                logger.failure(`Failed to check rewards for wallet`, { error: error.message });
            }
        }

        logger.success(`\n📊 Rewards Summary:`);
        logger.progress(`   • Wallets Checked: ${checkedWallets}`);
        logger.progress(`   • Total Claimable: ${totalClaimable} VET`);
        logger.progress(`   • Total Pending: ${totalPending} VET`);
    }

    async claimRewards() {
        logger.progress('💰 Claiming referral rewards for all eligible wallets...');
        
        const accounts = this.walletManager.accounts;
        if (accounts.length === 0) {
            logger.failure('No wallets found');
            return;
        }

        let totalClaimed = 0;
        let successfulClaims = 0;

        for (const account of accounts) {
            try {
                // Get wallet
                const wallet = await this.walletManager.getWallet();
                if (wallet.id !== account.id) continue;

                // Generate certificate
                const certificate = await this.generateCertificate(wallet);

                // Claim rewards
                const claimResult = await this.referralManager.claimReferralRewards(wallet, certificate);
                
                if (claimResult.success) {
                    totalClaimed += claimResult.claimedAmount || 0;
                    successfulClaims++;
                    logger.success(`✅ ${wallet.address}: Claimed ${claimResult.claimedAmount} VET`);
                } else if (claimResult.reason === 'no_rewards') {
                    logger.progress(`ℹ️  ${wallet.address}: No rewards to claim`);
                } else if (claimResult.reason === 'below_threshold') {
                    logger.progress(`⏳ ${wallet.address}: Rewards below threshold`);
                }

            } catch (error) {
                logger.failure(`Failed to claim rewards for wallet`, { error: error.message });
            }
        }

        logger.success(`\n🎉 Claim Summary:`);
        logger.progress(`   • Successful Claims: ${successfulClaims}`);
        logger.progress(`   • Total Claimed: ${totalClaimed} VET`);
    }

    async applyReferralToExistingWallets() {
        logger.progress('🎯 Applying referral code to existing wallets...');
        
        const accounts = this.walletManager.accounts;
        if (accounts.length === 0) {
            logger.failure('No wallets found');
            return;
        }

        let successfulApplications = 0;
        let alreadyApplied = 0;

        for (const account of accounts) {
            try {
                // Get wallet
                const wallet = await this.walletManager.getWallet();
                if (wallet.id !== account.id) continue;

                // Generate certificate
                const certificate = await this.generateCertificate(wallet);

                // Apply referral
                const referralResult = await this.referralManager.applyReferral(wallet, certificate);
                
                if (referralResult.success) {
                    successfulApplications++;
                    logger.success(`✅ ${wallet.address}: Referral applied successfully`);
                } else if (referralResult.reason === 'already_applied') {
                    alreadyApplied++;
                    logger.progress(`ℹ️  ${wallet.address}: Referral already applied`);
                } else {
                    logger.failure(`❌ ${wallet.address}: ${referralResult.reason}`);
                }

                // Add delay between applications
                await this.networkManager.sleep(2000);

            } catch (error) {
                logger.failure(`Failed to apply referral for wallet`, { error: error.message });
            }
        }

        logger.success(`\n📊 Application Summary:`);
        logger.progress(`   • Successful Applications: ${successfulApplications}`);
        logger.progress(`   • Already Applied: ${alreadyApplied}`);
        logger.progress(`   • Total Processed: ${accounts.length}`);
    }

    async generateCertificate(wallet) {
        const value = {
            user: wallet.address.toLowerCase(),
            isSocialLogin: false,
            timestamp: new Date().toISOString()
        };

        const domain = {
            name: 'Cleanify',
            version: '1',
            chainId: 1,
            verifyingContract: this.config.contract.verifyingContract
        };

        const types = {
            Authentication: [
                { name: 'user', type: 'address' },
                { name: 'isSocialLogin', type: 'bool' },
                { name: 'timestamp', type: 'string' }
            ]
        };

        const { Wallet } = await import('ethers');
        const signer = new Wallet(wallet.privateKey);
        const signature = await signer.signTypedData(domain, types, value);

        const certificate = { address: wallet.address, value, signature };
        const certB64 = Buffer.from(JSON.stringify(certificate)).toString('base64');

        return `certificate ${certB64}`;
    }
}

async function main() {
    const referralBot = new ReferralBot();
    
    try {
        await referralBot.initialize();
        
        const command = process.argv[2] || 'info';
        
        switch (command.toLowerCase()) {
            case 'info':
                await referralBot.showReferralInfo();
                break;
            case 'stats':
                await referralBot.showStats();
                break;
            case 'check':
                await referralBot.checkRewards();
                break;
            case 'claim':
                await referralBot.claimRewards();
                break;
            case 'apply':
                await referralBot.applyReferralToExistingWallets();
                break;
            default:
                logger.failure(`Unknown command: ${command}`);
                logger.progress('Available commands: info, stats, check, claim, apply');
                process.exit(1);
        }
        
    } catch (error) {
        logger.failure('Referral bot failed', { error: error.message });
        process.exit(1);
    }
}

main();
