import fetch from 'node-fetch';
import axios from 'axios';
import UserAgent from 'user-agents';
import { ProxyAgent } from 'proxy-agent';
import logger from './logger.js';

class NetworkManager {
    constructor(config) {
        this.config = config;
        this.userAgent = new UserAgent();
        this.setupAxios();
    }

    setupAxios() {
        // Create axios instance with default config
        this.axios = axios.create({
            timeout: this.config.app.requestTimeout || 30000,
            headers: {
                'Accept': '*/*',
                'Accept-Language': 'id-ID,id;q=0.9,en-US;q=0.8,en;q=0.7',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'cross-site'
            }
        });

        // Setup proxy if enabled
        if (this.config.security.useProxy && this.config.security.proxyUrl) {
            const agent = new ProxyAgent(this.config.security.proxyUrl);
            this.axios.defaults.httpAgent = agent;
            this.axios.defaults.httpsAgent = agent;
            logger.network('Proxy configured', { proxy: this.config.security.proxyUrl });
        }

        // Add request interceptor
        this.axios.interceptors.request.use(
            (config) => {
                // Randomize user agent if enabled
                if (this.config.security.randomizeUserAgent) {
                    config.headers['User-Agent'] = this.userAgent.random().toString();
                }
                
                logger.network('Request sent', {
                    method: config.method?.toUpperCase(),
                    url: config.url,
                    headers: Object.keys(config.headers || {})
                });
                
                return config;
            },
            (error) => {
                logger.failure('Request interceptor error', { error: error.message });
                return Promise.reject(error);
            }
        );

        // Add response interceptor
        this.axios.interceptors.response.use(
            (response) => {
                logger.network('Response received', {
                    status: response.status,
                    url: response.config.url,
                    size: response.headers['content-length'] || 'unknown'
                });
                return response;
            },
            (error) => {
                logger.network('Response error', {
                    status: error.response?.status,
                    url: error.config?.url,
                    message: error.message
                });
                return Promise.reject(error);
            }
        );
    }

    /**
     * Make HTTP request with retry logic
     * @param {Object} options - Request options
     * @returns {Promise} - Response data
     */
    async makeRequest(options) {
        const maxRetries = this.config.app.maxRetries || 3;
        const retryDelay = this.config.app.retryDelay || 2000;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.progress(`Request attempt ${attempt}/${maxRetries}`, {
                    method: options.method,
                    url: options.url
                });

                const response = await this.axios(options);
                
                logger.success('Request completed successfully', {
                    attempt,
                    status: response.status,
                    url: options.url
                });

                return response.data;

            } catch (error) {
                logger.failure(`Request attempt ${attempt} failed`, {
                    error: error.message,
                    status: error.response?.status,
                    url: options.url
                });

                // Don't retry on certain status codes
                if (error.response?.status && [400, 401, 403, 404, 422].includes(error.response.status)) {
                    throw error;
                }

                // If this was the last attempt, throw the error
                if (attempt === maxRetries) {
                    throw error;
                }

                // Wait before retrying
                const delay = retryDelay * attempt + (Math.random() * 1000);
                logger.progress(`Waiting ${delay}ms before retry...`);
                await this.sleep(delay);
            }
        }
    }

    /**
     * Upload file with form data
     * @param {string} url - Upload URL
     * @param {FormData} formData - Form data to upload
     * @param {Object} headers - Additional headers
     * @returns {Promise} - Upload response
     */
    async uploadFile(url, formData, headers = {}) {
        const options = {
            method: 'POST',
            url,
            data: formData,
            headers: {
                ...headers,
                ...formData.getHeaders?.() || {}
            },
            maxContentLength: Infinity,
            maxBodyLength: Infinity
        };

        return this.makeRequest(options);
    }

    /**
     * Sleep utility
     * @param {number} ms - Milliseconds to sleep
     * @returns {Promise} - Sleep promise
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Add random delay
     * @returns {Promise} - Delay promise
     */
    async randomDelay() {
        if (!this.config.automation.randomDelay) return;

        const maxDelay = this.config.automation.maxRandomDelay || 10000;
        const delay = Math.random() * maxDelay;
        
        logger.progress(`Adding random delay: ${Math.round(delay)}ms`);
        await this.sleep(delay);
    }
}

export default NetworkManager;
