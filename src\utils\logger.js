import winston from 'winston';
import fs from 'fs-extra';
import path from 'path';

// Ensure logs directory exists
await fs.ensureDir('./logs');

// Custom format for console output
const consoleFormat = winston.format.combine(
    winston.format.timestamp({ format: 'HH:mm:ss' }),
    winston.format.colorize(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
        let metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
        return `[${timestamp}] ${level}: ${message} ${metaStr}`;
    })
);

// Custom format for file output
const fileFormat = winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    transports: [
        // Console transport
        new winston.transports.Console({
            format: consoleFormat
        }),
        
        // File transport for all logs
        new winston.transports.File({
            filename: './logs/app.log',
            format: fileFormat,
            maxsize: 5242880, // 5MB
            maxFiles: 5
        }),
        
        // File transport for errors only
        new winston.transports.File({
            filename: './logs/error.log',
            level: 'error',
            format: fileFormat,
            maxsize: 5242880, // 5MB
            maxFiles: 5
        }),
        
        // File transport for success logs
        new winston.transports.File({
            filename: './logs/success.log',
            level: 'info',
            format: fileFormat,
            maxsize: 5242880, // 5MB
            maxFiles: 3,
            filter: (info) => info.message.includes('SUCCESS') || info.message.includes('COMPLETED')
        })
    ]
});

// Add custom methods
logger.success = (message, meta = {}) => {
    logger.info(`✅ SUCCESS: ${message}`, meta);
};

logger.failure = (message, meta = {}) => {
    logger.error(`❌ FAILURE: ${message}`, meta);
};

logger.progress = (message, meta = {}) => {
    logger.info(`⏳ PROGRESS: ${message}`, meta);
};

logger.security = (message, meta = {}) => {
    logger.warn(`🔒 SECURITY: ${message}`, meta);
};

logger.network = (message, meta = {}) => {
    logger.debug(`🌐 NETWORK: ${message}`, meta);
};

export default logger;
