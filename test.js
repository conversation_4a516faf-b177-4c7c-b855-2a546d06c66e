#!/usr/bin/env node

import fs from 'fs-extra';
import path from 'path';
import logger from './src/utils/logger.js';
import CryptoManager from './src/utils/crypto.js';
import NetworkManager from './src/utils/network.js';
import FileManager from './src/utils/fileManager.js';
import WalletManager from './src/wallet/walletManager.js';
import dotenv from 'dotenv';

dotenv.config();

class BotTester {
    constructor() {
        this.config = null;
        this.tests = [];
        this.passed = 0;
        this.failed = 0;
    }

    async initialize() {
        try {
            this.config = await fs.readJson('./config.json');
            logger.success('Test environment initialized');
        } catch (error) {
            logger.failure('Failed to initialize test environment', { error: error.message });
            throw error;
        }
    }

    addTest(name, testFn) {
        this.tests.push({ name, testFn });
    }

    async runTest(test) {
        try {
            logger.progress(`Running test: ${test.name}`);
            await test.testFn();
            logger.success(`✅ ${test.name}`);
            this.passed++;
        } catch (error) {
            logger.failure(`❌ ${test.name}`, { error: error.message });
            this.failed++;
        }
    }

    async runAllTests() {
        logger.success('🧪 Starting Bot Tests');
        logger.progress('================================');

        for (const test of this.tests) {
            await this.runTest(test);
        }

        logger.progress('================================');
        logger.success(`Test Results: ${this.passed} passed, ${this.failed} failed`);
        
        if (this.failed > 0) {
            process.exit(1);
        }
    }

    // Test crypto manager
    async testCrypto() {
        const crypto = new CryptoManager(process.env.ENCRYPTION_KEY);
        
        const testData = 'test-private-key-12345';
        const encrypted = crypto.encrypt(testData);
        const decrypted = crypto.decrypt(encrypted);
        
        if (decrypted !== testData) {
            throw new Error('Encryption/decryption failed');
        }

        const hash1 = crypto.hash('test');
        const hash2 = crypto.hash('test');
        
        if (hash1 !== hash2) {
            throw new Error('Hash function inconsistent');
        }

        if (!crypto.verifyHash('test', hash1)) {
            throw new Error('Hash verification failed');
        }
    }

    // Test network manager
    async testNetwork() {
        const network = new NetworkManager(this.config);
        
        // Test basic request
        try {
            await network.makeRequest({
                method: 'GET',
                url: 'https://httpbin.org/status/200',
                timeout: 5000
            });
        } catch (error) {
            throw new Error(`Network request failed: ${error.message}`);
        }
    }

    // Test file manager
    async testFileManager() {
        const fileManager = new FileManager(this.config);
        
        // Create test image
        const testImagePath = './test_image.png';
        const testImageData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', 'base64');
        await fs.writeFile(testImagePath, testImageData);

        try {
            // Test image validation
            const validation = await fileManager.validateImage(testImagePath);
            if (!validation.valid) {
                throw new Error('Image validation failed');
            }

            // Test getting images
            const images = await fileManager.getImages('./');
            if (!images.includes(testImagePath)) {
                throw new Error('Failed to find test image');
            }

        } finally {
            // Cleanup
            if (await fs.pathExists(testImagePath)) {
                await fs.remove(testImagePath);
            }
        }
    }

    // Test wallet manager
    async testWalletManager() {
        const crypto = new CryptoManager(process.env.ENCRYPTION_KEY);
        const walletManager = new WalletManager(this.config, crypto);
        
        // Test wallet creation
        const wallet = await walletManager.getWallet();
        
        if (!wallet.address || !wallet.privateKey || !wallet.mnemonic) {
            throw new Error('Wallet creation failed - missing required fields');
        }

        if (!wallet.address.startsWith('0x')) {
            throw new Error('Invalid wallet address format');
        }

        // Test wallet saving
        await walletManager.saveWallet(wallet, true);
        
        const stats = walletManager.getStats();
        if (stats.totalWallets === 0) {
            throw new Error('Wallet was not saved properly');
        }
    }

    // Test configuration
    async testConfig() {
        const requiredFields = [
            'app.name',
            'app.version',
            'security.encryptPrivateKeys',
            'api.ipfsUrl',
            'api.dailyActionUrl',
            'contract.rewardClaimerAddress'
        ];

        for (const field of requiredFields) {
            const value = field.split('.').reduce((obj, key) => obj?.[key], this.config);
            if (value === undefined || value === null) {
                throw new Error(`Missing required config field: ${field}`);
            }
        }

        // Test environment variables
        const requiredEnvVars = [
            'ENCRYPTION_KEY',
            'IPFS_API_URL',
            'DAILY_ACTION_API_URL'
        ];

        for (const envVar of requiredEnvVars) {
            if (!process.env[envVar]) {
                throw new Error(`Missing required environment variable: ${envVar}`);
            }
        }
    }

    // Test directories
    async testDirectories() {
        const requiredDirs = [
            './images',
            './data',
            './logs',
            './src/utils',
            './src/wallet'
        ];

        for (const dir of requiredDirs) {
            if (!await fs.pathExists(dir)) {
                throw new Error(`Required directory missing: ${dir}`);
            }
        }
    }

    // Test file permissions
    async testPermissions() {
        const testFile = './data/test_permissions.txt';
        
        try {
            await fs.writeFile(testFile, 'test');
            await fs.readFile(testFile);
            await fs.remove(testFile);
        } catch (error) {
            throw new Error(`File permission test failed: ${error.message}`);
        }
    }
}

async function main() {
    const tester = new BotTester();
    
    try {
        await tester.initialize();
        
        // Add all tests
        tester.addTest('Configuration Test', () => tester.testConfig());
        tester.addTest('Directory Structure Test', () => tester.testDirectories());
        tester.addTest('File Permissions Test', () => tester.testPermissions());
        tester.addTest('Crypto Manager Test', () => tester.testCrypto());
        tester.addTest('Network Manager Test', () => tester.testNetwork());
        tester.addTest('File Manager Test', () => tester.testFileManager());
        tester.addTest('Wallet Manager Test', () => tester.testWalletManager());
        
        // Run all tests
        await tester.runAllTests();
        
        logger.success('🎉 All tests passed! Bot is ready to use.');
        
    } catch (error) {
        logger.failure('Test suite failed', { error: error.message });
        process.exit(1);
    }
}

main();
