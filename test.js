#!/usr/bin/env node

import fs from 'fs-extra';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config();

async function runBasicTests() {
    console.log('🧪 Running Basic Tests for Cleanify Bot v2.0');
    console.log('==============================================');

    let passed = 0;
    let failed = 0;

    // Test 1: Check if config.json exists
    try {
        await fs.readJson('./config.json');
        console.log('✅ Config file exists and is valid JSON');
        passed++;
    } catch (error) {
        console.log('❌ Config file test failed:', error.message);
        failed++;
    }

    // Test 2: Check if .env exists
    try {
        if (await fs.pathExists('./.env')) {
            console.log('✅ Environment file exists');
            passed++;
        } else {
            console.log('❌ Environment file missing');
            failed++;
        }
    } catch (error) {
        console.log('❌ Environment file test failed:', error.message);
        failed++;
    }

    // Test 3: Check required directories
    const requiredDirs = ['./src/utils', './src/wallet', './src/referral', './data', './logs', './images'];
    let dirTestPassed = true;

    for (const dir of requiredDirs) {
        if (!await fs.pathExists(dir)) {
            console.log(`❌ Required directory missing: ${dir}`);
            dirTestPassed = false;
        }
    }

    if (dirTestPassed) {
        console.log('✅ All required directories exist');
        passed++;
    } else {
        failed++;
    }

    // Test 4: Check if encryption key is set
    if (process.env.ENCRYPTION_KEY && process.env.ENCRYPTION_KEY !== 'your-super-secret-encryption-key-change-this-immediately') {
        console.log('✅ Encryption key is configured');
        passed++;
    } else {
        console.log('❌ Encryption key not configured properly');
        failed++;
    }

    // Test 5: Check referral configuration
    if (process.env.REFERRAL_CODE === 'LD4BK') {
        console.log('✅ Referral code configured correctly');
        passed++;
    } else {
        console.log('❌ Referral code not configured');
        failed++;
    }

    console.log('\n==============================================');
    console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);

    if (failed === 0) {
        console.log('🎉 All basic tests passed! Bot should be ready to use.');
        console.log('\n🚀 Next steps:');
        console.log('1. Add images to ./images/ directory');
        console.log('2. Run: npm start');
        console.log('3. Check referral stats: npm run referral:stats');
    } else {
        console.log('⚠️  Some tests failed. Please fix the issues above.');
        process.exit(1);
    }
}

runBasicTests().catch(error => {
    console.error('Test failed:', error.message);
    process.exit(1);
});
